{"name": "truck-parts-manager", "version": "1.0.0", "description": "تطبيق إدارة متجر قطع غيار الشاحنات - Truck Parts Store Management Application", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run dev:react\" \"npm run dev:electron\"", "dev:react": "vite", "dev:electron": "wait-on http://localhost:5173 && electron . --no-sandbox", "build": "npm run build:react && npm run build:electron", "build:react": "vite build", "build:electron": "tsc -p tsconfig.electron.json", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux"}, "keywords": ["truck", "parts", "inventory", "management", "arabic", "electron", "react"], "author": "Truck Parts Manager", "license": "MIT", "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "wait-on": "^7.2.0"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.2", "@mui/material": "^5.15.2", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "better-sqlite3": "^9.2.2", "dayjs": "^1.11.10", "i18next": "^23.7.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "recharts": "^2.15.3"}, "build": {"appId": "com.truckparts.manager", "productName": "Truck Parts Manager", "directories": {"output": "release"}, "files": ["dist/**/*", "database/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}