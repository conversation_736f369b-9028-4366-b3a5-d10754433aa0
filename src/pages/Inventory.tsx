import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Warning as WarningIcon,
  Inventory as InventoryIcon,
  QrCode as QrCodeIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useNotifications } from '../contexts/NotificationContext';
import { dbService } from '../services/database';
import { dbInitService } from '../services/databaseInit';
import LowStockTab from '../components/Inventory/LowStockTab';

// تعريف نوع قطعة الغيار
interface Part {
  id: number;
  part_number: string;
  part_name: string;
  barcode?: string;
  category: string;
  quantity: number;
  purchase_price: number;
  selling_price: number;
  min_quantity: number;
  shelf_location: string;
  compatible_models?: string;
  last_sale_date?: string;
  last_purchase_date?: string;
  preferred_supplier: string;
  is_active: boolean;
}

// مكون نموذج إضافة/تعديل قطعة
interface PartDialogProps {
  open: boolean;
  onClose: () => void;
  part?: Part | null;
  onSave: (part: Partial<Part>) => void;
}

const PartDialog: React.FC<PartDialogProps> = ({ open, onClose, part, onSave }) => {
  const { t } = useTranslation();
  const [barcodeError, setBarcodeError] = useState('');
  const [formData, setFormData] = useState<Partial<Part>>({
    part_number: '',
    part_name: '',
    barcode: '',
    category: '',
    quantity: 0,
    purchase_price: 0,
    selling_price: 0,
    min_quantity: 5,
    shelf_location: '',
    compatible_models: '',
    is_active: true,
  });

  useEffect(() => {
    if (part) {
      setFormData(part);
    } else {
      setFormData({
        part_number: '',
        part_name: '',
        barcode: '',
        category: '',
        quantity: 0,
        purchase_price: 0,
        selling_price: 0,
        min_quantity: 5,
        shelf_location: '',
        compatible_models: '',
        is_active: true,
      });
    }
  }, [part, open]);

  const handleChange = (field: keyof Part) => (event: any) => {
    const value = event.target.value;

    // التحقق من عدم تكرار الباركود
    if (field === 'barcode' && value) {
      // نحتاج للوصول إلى قائمة القطع من المكون الرئيسي
      // سنضيف هذا التحقق في المكون الرئيسي
      setBarcodeError('');
    }

    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {part ? t('editPart') : t('addPart')}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('partNumber')}
              value={formData.part_number || ''}
              onChange={handleChange('part_number')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="الباركود/الرمز الشريطي"
              value={formData.barcode || ''}
              onChange={handleChange('barcode')}
              placeholder="أدخل رقم الباركود أو اتركه فارغاً"
              error={!!barcodeError}
              helperText={barcodeError || 'حقل اختياري - يمكن تركه فارغاً'}
              sx={{
                '& .MuiInputBase-root': {
                  fontFamily: 'monospace',
                },
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif',
                },
                '& .MuiFormHelperText-root': {
                  fontFamily: 'Cairo, sans-serif',
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('partName')}
              value={formData.part_name || ''}
              onChange={handleChange('part_name')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>{t('category')}</InputLabel>
              <Select
                value={formData.category || ''}
                onChange={handleChange('category')}
                label={t('category')}
              >
                <MenuItem value="محرك">محرك</MenuItem>
                <MenuItem value="فرامل">فرامل</MenuItem>
                <MenuItem value="إطارات">إطارات</MenuItem>
                <MenuItem value="كهرباء">كهرباء</MenuItem>
                <MenuItem value="تكييف">تكييف</MenuItem>
                <MenuItem value="أخرى">أخرى</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('location')}
              value={formData.shelf_location || ''}
              onChange={handleChange('shelf_location')}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="الموديلات المتوافقة"
              multiline
              rows={3}
              value={formData.compatible_models || ''}
              onChange={handleChange('compatible_models')}
              placeholder="أدخل موديلات الشاحنات المتوافقة، مفصولة بفواصل (مثال: Mercedes Actros, Volvo FH, Scania R-Series)"
              sx={{
                '& .MuiInputBase-root': {
                  fontFamily: 'Cairo, sans-serif',
                },
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif',
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label={t('quantity')}
              type="number"
              value={formData.quantity || 0}
              onChange={handleChange('quantity')}
              required
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label={t('minQuantity')}
              type="number"
              value={formData.min_quantity || 5}
              onChange={handleChange('min_quantity')}
              required
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={formData.is_active ? 1 : 0}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 1 }))}
                label="الحالة"
              >
                <MenuItem value={1}>نشط</MenuItem>
                <MenuItem value={0}>غير نشط</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('purchasePrice')}
              type="number"
              value={formData.purchase_price || 0}
              onChange={handleChange('purchase_price')}
              required
              InputProps={{
                endAdornment: <Typography variant="body2">دج</Typography>,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('sellingPrice')}
              type="number"
              value={formData.selling_price || 0}
              onChange={handleChange('selling_price')}
              required
              InputProps={{
                endAdornment: <Typography variant="body2">دج</Typography>,
              }}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const Inventory: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { addNotification } = useNotifications();
  const [parts, setParts] = useState<Part[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'part_number',
      headerName: t('partNumber'),
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'part_name',
      headerName: t('partName'),
      width: 200,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'barcode',
      headerName: 'الباركود',
      width: 150,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <QrCodeIcon sx={{ color: '#64748b', fontSize: 16 }} />
          <Typography
            sx={{
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              color: 'text.primary',
            }}
            title={params.value || ''}
          >
            {params.value || '-'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'category',
      headerName: t('category'),
      width: 120,
      renderCell: (params) => (
        <Chip label={params.value} size="small" color="primary" />
      ),
    },
    {
      field: 'quantity',
      headerName: t('quantity'),
      width: 100,
      renderCell: (params) => {
        const isLowStock = params.value <= params.row.min_quantity;
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {isLowStock && <WarningIcon sx={{ color: '#f59e0b', fontSize: 16, mr: 0.5 }} />}
            <Typography
              sx={{
                fontFamily: 'Cairo, sans-serif',
                color: isLowStock ? '#f59e0b' : 'inherit',
                fontWeight: isLowStock ? 'bold' : 'normal',
              }}
            >
              {params.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'selling_price',
      headerName: t('sellingPrice'),
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'shelf_location',
      headerName: t('location'),
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value || '-'}
        </Typography>
      ),
    },
    {
      field: 'compatible_models',
      headerName: 'الموديلات المتوافقة',
      width: 200,
      renderCell: (params) => (
        <Typography
          sx={{
            fontFamily: 'Cairo, sans-serif',
            fontSize: '0.875rem',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
          title={params.value || ''}
        >
          {params.value || '-'}
        </Typography>
      ),
    },
    {
      field: 'is_active',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          size="small"
          color={params.value ? 'success' : 'default'}
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 120,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEdit(params.row)}
            sx={{ color: '#3b82f6' }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(params.row.id)}
            sx={{ color: '#ef4444' }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadParts();
  }, []);

  // فتح النموذج تلقائياً عند القدوم من لوحة التحكم
  useEffect(() => {
    if (location.state?.openDialog) {
      setDialogOpen(true);
      // مسح الـ state لتجنب فتح النموذج مرة أخرى عند إعادة التحميل
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const loadParts = async () => {
    try {
      setLoading(true);

      // تهيئة قاعدة البيانات مع البيانات التجريبية إذا لزم الأمر
      await dbInitService.initializeDatabase();

      // تحميل البيانات من قاعدة البيانات
      const dbParts = await dbService.getAllParts();

      // تحويل البيانات إلى التنسيق المطلوب
      const parts: Part[] = dbParts.map(part => ({
        id: part.part_id,
        part_number: part.part_number,
        part_name: part.part_name,
        barcode: part.barcode,
        category: 'محرك', // سيتم تحسينها لاحقاً مع جدول الفئات
        quantity: part.quantity,
        purchase_price: part.purchase_price,
        selling_price: part.selling_price,
        min_quantity: part.min_quantity,
        shelf_location: part.shelf_location || '',
        compatible_models: '', // سيتم تحسينها لاحقاً
        last_sale_date: '',
        last_purchase_date: '',
        preferred_supplier: '', // سيتم تحسينها لاحقاً
        is_active: part.is_active,
      }));

      // إذا لم توجد بيانات، استخدم البيانات التجريبية
      if (parts.length === 0) {
        const mockParts: Part[] = [
        {
          id: 1,
          part_number: 'ENG001',
          part_name: 'فلتر زيت محرك',
          barcode: '1234567890123',
          category: 'محرك',
          quantity: 25,
          purchase_price: 1500,
          selling_price: 2000,
          min_quantity: 10,
          shelf_location: 'A1-01',
          compatible_models: 'Mercedes Actros, Volvo FH, Scania R-Series',
          last_sale_date: '2024-05-28',
          last_purchase_date: '2024-05-15',
          preferred_supplier: 'شركة قطع الغيار المتقدمة',
          is_active: true,
        },
        {
          id: 2,
          part_number: 'BRK002',
          part_name: 'أقراص فرامل أمامية',
          barcode: '2345678901234',
          category: 'فرامل',
          quantity: 5,
          purchase_price: 3000,
          selling_price: 4000,
          min_quantity: 8,
          shelf_location: 'B2-03',
          compatible_models: 'MAN TGX, DAF XF, Iveco Stralis',
          last_sale_date: '2024-05-25',
          last_purchase_date: '2024-04-20',
          preferred_supplier: 'مؤسسة الشاحنات الحديثة',
          is_active: true,
        },
        {
          id: 3,
          part_number: 'TIR003',
          part_name: 'إطار 315/80R22.5',
          barcode: '3456789012345',
          category: 'إطارات',
          quantity: 12,
          purchase_price: 25000,
          selling_price: 32000,
          min_quantity: 5,
          shelf_location: 'C1-01',
          compatible_models: 'جميع الشاحنات الثقيلة, Mercedes Actros, Volvo FH, Scania R-Series, MAN TGX',
          last_sale_date: '2024-05-30',
          last_purchase_date: '2024-05-10',
          preferred_supplier: 'شركة الإمداد السريع',
          is_active: true,
        },
        {
          id: 4,
          part_number: 'ELC004',
          part_name: 'بطارية 12V 100Ah',
          barcode: '4567890123456',
          category: 'كهرباء',
          quantity: 2,
          purchase_price: 15000,
          selling_price: 20000,
          min_quantity: 5,
          shelf_location: 'D1-02',
          compatible_models: 'Volvo FH, Scania R-Series, Renault Magnum',
          last_sale_date: '2024-05-20',
          last_purchase_date: '2024-04-01',
          preferred_supplier: 'شركة قطع الغيار المتقدمة',
          is_active: true,
        },
        {
          id: 5,
          part_number: 'AIR005',
          part_name: 'فلتر هواء كابينة',
          category: 'تكييف',
          quantity: 0,
          purchase_price: 800,
          selling_price: 1200,
          min_quantity: 10,
          shelf_location: 'E2-01',
          compatible_models: 'Mercedes Actros, MAN TGX, DAF XF',
          last_sale_date: '2024-05-29',
          last_purchase_date: '2024-03-15',
          preferred_supplier: 'مؤسسة الشاحنات الحديثة',
          is_active: true,
        },
      ];
        setParts(mockParts);
      } else {
        setParts(parts);
      }

      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error loading parts:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedPart(null);
    setDialogOpen(true);
  };

  const handleEdit = (part: Part) => {
    setSelectedPart(part);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه القطعة؟')) {
      try {
        await dbService.deletePart(id);
        setParts(prev => prev.filter(part => part.id !== id));
        setSnackbar({ open: true, message: 'تم حذف القطعة بنجاح', severity: 'success' });
      } catch (error) {
        console.error('Error deleting part:', error);
        setSnackbar({ open: true, message: 'خطأ في حذف القطعة', severity: 'error' });
      }
    }
  };

  const handleSave = async (partData: Partial<Part>) => {
    try {
      if (selectedPart) {
        // تحديث قطعة موجودة
        const updateData = {
          part_number: partData.part_number,
          part_name: partData.part_name,
          part_name_en: partData.part_name || '', // مؤقت
          barcode: partData.barcode,
          purchase_price: partData.purchase_price,
          selling_price: partData.selling_price,
          quantity: partData.quantity,
          min_quantity: partData.min_quantity,
          shelf_location: partData.shelf_location,
          is_active: partData.is_active,
        };

        await dbService.updatePart(selectedPart.id, updateData);

        setParts(prev => prev.map(part =>
          part.id === selectedPart.id ? { ...part, ...partData } : part
        ));
        setSnackbar({ open: true, message: 'تم تحديث القطعة بنجاح', severity: 'success' });
      } else {
        // إضافة قطعة جديدة
        const newPartData = {
          part_number: partData.part_number || '',
          part_name: partData.part_name || '',
          part_name_en: partData.part_name || '', // مؤقت
          barcode: partData.barcode,
          purchase_price: partData.purchase_price || 0,
          selling_price: partData.selling_price || 0,
          quantity: partData.quantity || 0,
          min_quantity: partData.min_quantity || 5,
          shelf_location: partData.shelf_location,
          is_active: partData.is_active !== false,
        };

        const newId = await dbService.createPart(newPartData);

        const newPart: Part = {
          ...partData as Part,
          id: newId,
        };
        setParts(prev => [...prev, newPart]);
        setSnackbar({ open: true, message: 'تم إضافة القطعة بنجاح', severity: 'success' });
      }
      setDialogOpen(false);
      setSelectedPart(null);
    } catch (error) {
      console.error('Error saving part:', error);
      setSnackbar({ open: true, message: 'خطأ في حفظ القطعة', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filteredParts = parts.filter(part => {
    const matchesSearch = part.part_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         part.part_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (part.barcode && part.barcode.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = !categoryFilter || part.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  // حساب القطع منخفضة المخزون
  const lowStockParts = parts.filter(part => part.quantity <= part.min_quantity);
  const outOfStockParts = parts.filter(part => part.quantity === 0);
  const lowStockCount = lowStockParts.length;

  // إضافة تنبيهات للمخزون المنخفض
  useEffect(() => {
    if (lowStockParts.length > 0) {
      lowStockParts.forEach(part => {
        if (part.quantity === 0) {
          addNotification({
            type: 'error',
            title: 'نفاد المخزون',
            message: `نفد مخزون ${part.part_name} (${part.part_number})`,
            priority: 'high',
            actionUrl: '/inventory',
          });
        } else if (part.quantity <= part.min_quantity) {
          addNotification({
            type: 'warning',
            title: 'مخزون منخفض',
            message: `${part.part_name} وصل للحد الأدنى - الكمية: ${part.quantity}`,
            priority: 'medium',
            actionUrl: '/inventory',
          });
        }
      });
    }
  }, [parts, addNotification]);

  const handleCreatePurchaseOrder = (part: Part) => {
    // سيتم ربطها بصفحة طلبيات الشراء
    addNotification({
      type: 'system',
      title: 'إنشاء طلبية شراء',
      message: `تم إنشاء طلبية شراء لـ ${part.part_name}`,
      priority: 'medium',
    });
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('inventory')}
      </Typography>

      {/* تنبيه المخزون المنخفض */}
      {lowStockCount > 0 && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            تحذير: يوجد {lowStockCount} قطع وصلت للحد الأدنى من المخزون ({outOfStockParts.length} نفدت تماماً)
          </Typography>
        </Alert>
      )}

      {/* التبويبات */}
      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontFamily: 'Cairo, sans-serif',
              fontWeight: 500,
            },
          }}
        >
          <Tab
            icon={<InventoryIcon />}
            label="جميع القطع"
            iconPosition="start"
          />
          <Tab
            icon={<WarningIcon />}
            label={`المخزون المنخفض (${lowStockCount})`}
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {tabValue === 0 && (
            <>
              {/* شريط الأدوات */}
              <Paper sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      placeholder="البحث بالاسم، رقم القطعة، أو الباركود..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      InputProps={{
                        startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
                      }}
                      sx={{
                        '& .MuiInputBase-input::placeholder': {
                          fontFamily: 'Cairo, sans-serif',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <InputLabel>تصفية حسب الفئة</InputLabel>
                      <Select
                        value={categoryFilter}
                        onChange={(e) => setCategoryFilter(e.target.value)}
                        label="تصفية حسب الفئة"
                      >
                        <MenuItem value="">جميع الفئات</MenuItem>
                        <MenuItem value="محرك">محرك</MenuItem>
                        <MenuItem value="فرامل">فرامل</MenuItem>
                        <MenuItem value="إطارات">إطارات</MenuItem>
                        <MenuItem value="كهرباء">كهرباء</MenuItem>
                        <MenuItem value="تكييف">تكييف</MenuItem>
                        <MenuItem value="أخرى">أخرى</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={5} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={handleAdd}
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {t('addPart')}
                    </Button>
                  </Grid>
                </Grid>
              </Paper>

              {/* جدول البيانات */}
              <Paper sx={{ height: 600, width: '100%' }}>
                <DataGrid
                  rows={filteredParts}
                  columns={columns}
                  loading={loading}
                  pageSizeOptions={[10, 25, 50]}
                  initialState={{
                    pagination: {
                      paginationModel: { page: 0, pageSize: 10 },
                    },
                  }}
                  sx={{
                    '& .MuiDataGrid-root': {
                      fontFamily: 'Cairo, sans-serif',
                    },
                  }}
                />
              </Paper>
            </>
          )}

          {tabValue === 1 && (
            <LowStockTab
              lowStockParts={lowStockParts}
              onCreatePurchaseOrder={handleCreatePurchaseOrder}
            />
          )}
        </Box>
      </Paper>

      {/* نموذج إضافة/تعديل */}
      <PartDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        part={selectedPart}
        onSave={handleSave}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Inventory;
