import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tabs,
  Tab,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  AccountBalance as DebtIcon,
  Payment as PaymentIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';

// تعريف أنواع البيانات
interface Debt {
  id: number;
  customer_id: number;
  customer_name: string;
  customer_type: 'individual' | 'company' | 'workshop' | 'fleet_owner';
  debt_amount: number;
  paid_amount: number;
  remaining_amount: number;
  due_date: string;
  status: 'pending' | 'overdue' | 'partially_paid' | 'fully_paid';
  notes: string;
  created_at: string;
  updated_at: string;
  payments?: Payment[];
}

interface Payment {
  id: number;
  debt_id: number;
  amount: number;
  payment_date: string;
  payment_method: 'cash' | 'bank_transfer' | 'check';
  notes: string;
  created_at: string;
}

// مكون نموذج إضافة/تعديل دين
interface DebtDialogProps {
  open: boolean;
  onClose: () => void;
  debt?: Debt | null;
  onSave: (debt: Partial<Debt>) => void;
}

const DebtDialog: React.FC<DebtDialogProps> = ({ open, onClose, debt, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<Debt>>({
    customer_name: '',
    customer_type: 'individual',
    debt_amount: 0,
    due_date: '',
    notes: '',
  });

  useEffect(() => {
    if (debt) {
      setFormData(debt);
    } else {
      setFormData({
        customer_name: '',
        customer_type: 'individual',
        debt_amount: 0,
        due_date: '',
        notes: '',
      });
    }
  }, [debt, open]);

  const handleChange = (field: keyof Debt) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {debt ? t('editDebt') : t('addDebt')}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('customerName')}
              value={formData.customer_name || ''}
              onChange={handleChange('customer_name')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>{t('customerType')}</InputLabel>
              <Select
                value={formData.customer_type || 'individual'}
                onChange={handleChange('customer_type')}
                label={t('customerType')}
              >
                <MenuItem value="individual">فرد</MenuItem>
                <MenuItem value="company">شركة</MenuItem>
                <MenuItem value="workshop">ورشة</MenuItem>
                <MenuItem value="fleet_owner">مالك أسطول</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('debtAmount')}
              type="number"
              value={formData.debt_amount || 0}
              onChange={handleChange('debt_amount')}
              required
              InputProps={{
                endAdornment: <Typography variant="body2">دج</Typography>,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('dueDate')}
              type="date"
              value={formData.due_date || ''}
              onChange={handleChange('due_date')}
              InputLabelProps={{
                shrink: true,
              }}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('notes')}
              multiline
              rows={3}
              value={formData.notes || ''}
              onChange={handleChange('notes')}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// مكون نموذج تسجيل دفعة
interface PaymentDialogProps {
  open: boolean;
  onClose: () => void;
  debt: Debt | null;
  onSave: (payment: Partial<Payment>) => void;
}

const PaymentDialog: React.FC<PaymentDialogProps> = ({ open, onClose, debt, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<Payment>>({
    amount: 0,
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: 'cash',
    notes: '',
  });

  useEffect(() => {
    if (open) {
      setFormData({
        amount: debt?.remaining_amount || 0,
        payment_date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
        notes: '',
      });
    }
  }, [open, debt]);

  const handleChange = (field: keyof Payment) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = () => {
    if (debt) {
      onSave({ ...formData, debt_id: debt.id });
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {t('recordPayment')}
      </DialogTitle>
      <DialogContent>
        {debt && (
          <Box sx={{ mb: 2, p: 2, backgroundColor: '#f8fafc', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', mb: 1 }}>
              {debt.customer_name}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              المبلغ المتبقي: {debt.remaining_amount.toLocaleString()} دج
            </Typography>
          </Box>
        )}
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('paymentAmount')}
              type="number"
              value={formData.amount || 0}
              onChange={handleChange('amount')}
              required
              inputProps={{ min: 0, max: debt?.remaining_amount || 0 }}
              InputProps={{
                endAdornment: <Typography variant="body2">دج</Typography>,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('paymentDate')}
              type="date"
              value={formData.payment_date || ''}
              onChange={handleChange('payment_date')}
              InputLabelProps={{
                shrink: true,
              }}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>{t('paymentMethod')}</InputLabel>
              <Select
                value={formData.payment_method || 'cash'}
                onChange={handleChange('payment_method')}
                label={t('paymentMethod')}
              >
                <MenuItem value="cash">{t('cash')}</MenuItem>
                <MenuItem value="bank_transfer">{t('bankTransfer')}</MenuItem>
                <MenuItem value="check">{t('check')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('notes')}
              multiline
              rows={2}
              value={formData.notes || ''}
              onChange={handleChange('notes')}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('recordPayment')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// مكون عرض تاريخ الدفعات
interface PaymentHistoryProps {
  debt: Debt | null;
  onClose: () => void;
}

const PaymentHistory: React.FC<PaymentHistoryProps> = ({ debt, onClose }) => {
  const { t } = useTranslation();

  if (!debt) return null;

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return t('cash');
      case 'bank_transfer': return t('bankTransfer');
      case 'check': return t('check');
      default: return method;
    }
  };

  return (
    <Dialog open={!!debt} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {t('paymentHistory')} - {debt.customer_name}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2, p: 2, backgroundColor: '#f8fafc', borderRadius: 1 }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                إجمالي الدين: {debt.debt_amount.toLocaleString()} دج
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                المبلغ المدفوع: {debt.paid_amount.toLocaleString()} دج
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                المبلغ المتبقي: {debt.remaining_amount.toLocaleString()} دج
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <LinearProgress
                variant="determinate"
                value={(debt.paid_amount / debt.debt_amount) * 100}
                sx={{ mt: 1 }}
              />
            </Grid>
          </Grid>
        </Box>

        <List>
          {debt.payments && debt.payments.length > 0 ? (
            debt.payments.map((payment, index) => (
              <React.Fragment key={payment.id}>
                <ListItem>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                          {payment.amount.toLocaleString()} دج
                        </Typography>
                        <Chip
                          label={getPaymentMethodText(payment.payment_method)}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                          التاريخ: {new Date(payment.payment_date).toLocaleDateString('ar-DZ')}
                        </Typography>
                        {payment.notes && (
                          <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b', fontSize: '0.875rem' }}>
                            ملاحظات: {payment.notes}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
                {index < debt.payments.length - 1 && <Divider />}
              </React.Fragment>
            ))
          ) : (
            <Typography sx={{ textAlign: 'center', color: '#64748b', py: 4 }}>
              لا توجد دفعات مسجلة
            </Typography>
          )}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const Debts: React.FC = () => {
  const { t } = useTranslation();
  const [debts, setDebts] = useState<Debt[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [debtDialogOpen, setDebtDialogOpen] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [selectedDebt, setSelectedDebt] = useState<Debt | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'customer_info',
      headerName: 'العميل',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {params.row.customer_type === 'company' ? (
            <BusinessIcon sx={{ mr: 1, color: '#64748b' }} />
          ) : (
            <PersonIcon sx={{ mr: 1, color: '#64748b' }} />
          )}
          <Box>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold', fontSize: '0.875rem' }}>
              {params.row.customer_name}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontSize: '0.75rem', color: '#64748b' }}>
              {params.row.customer_type === 'individual' ? 'فرد' :
               params.row.customer_type === 'company' ? 'شركة' :
               params.row.customer_type === 'workshop' ? 'ورشة' : 'مالك أسطول'}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'debt_amount',
      headerName: 'مبلغ الدين',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'paid_amount',
      headerName: 'المبلغ المدفوع',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#10b981' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'remaining_amount',
      headerName: 'المبلغ المتبقي',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#ef4444', fontWeight: 'bold' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'due_date',
      headerName: 'تاريخ الاستحقاق',
      width: 130,
      renderCell: (params) => {
        const isOverdue = new Date(params.value) < new Date() && params.row.status !== 'fully_paid';
        return (
          <Typography sx={{
            fontFamily: 'Cairo, sans-serif',
            color: isOverdue ? '#ef4444' : '#64748b'
          }}>
            {new Date(params.value).toLocaleDateString('ar-DZ')}
          </Typography>
        );
      },
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params) => {
        const getStatusConfig = (status: string) => {
          switch (status) {
            case 'pending':
              return { label: 'في الانتظار', color: 'warning' as const, icon: <ScheduleIcon /> };
            case 'overdue':
              return { label: 'متأخر', color: 'error' as const, icon: <WarningIcon /> };
            case 'partially_paid':
              return { label: 'مدفوع جزئياً', color: 'info' as const, icon: <PaymentIcon /> };
            case 'fully_paid':
              return { label: 'مدفوع بالكامل', color: 'success' as const, icon: <CheckIcon /> };
            default:
              return { label: status, color: 'default' as const, icon: <ScheduleIcon /> };
          }
        };

        const config = getStatusConfig(params.value);
        return (
          <Chip
            icon={config.icon}
            label={config.label}
            size="small"
            color={config.color}
          />
        );
      },
    },
    {
      field: 'progress',
      headerName: 'التقدم',
      width: 120,
      renderCell: (params) => {
        const progress = (params.row.paid_amount / params.row.debt_amount) * 100;
        return (
          <Box sx={{ width: '100%' }}>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption" sx={{ fontFamily: 'Cairo, sans-serif' }}>
              {progress.toFixed(0)}%
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEdit(params.row)}
            sx={{ color: '#3b82f6' }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleRecordPayment(params.row)}
            sx={{ color: '#10b981' }}
            disabled={params.row.status === 'fully_paid'}
          >
            <PaymentIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleViewHistory(params.row)}
            sx={{ color: '#f59e0b' }}
          >
            <ScheduleIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(params.row.id)}
            sx={{ color: '#ef4444' }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadDebts();
  }, []);

  const loadDebts = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات - سيتم استبدالها بـ API حقيقي
      const mockDebts: Debt[] = [
        {
          id: 1,
          customer_id: 1,
          customer_name: 'شركة النقل السريع',
          customer_type: 'company',
          debt_amount: 450000,
          paid_amount: 200000,
          remaining_amount: 250000,
          due_date: '2024-06-15',
          status: 'partially_paid',
          notes: 'دين من فاتورة رقم 1001',
          created_at: '2024-05-01',
          updated_at: '2024-05-20',
          payments: [
            {
              id: 1,
              debt_id: 1,
              amount: 100000,
              payment_date: '2024-05-10',
              payment_method: 'bank_transfer',
              notes: 'دفعة أولى',
              created_at: '2024-05-10',
            },
            {
              id: 2,
              debt_id: 1,
              amount: 100000,
              payment_date: '2024-05-20',
              payment_method: 'cash',
              notes: 'دفعة ثانية',
              created_at: '2024-05-20',
            },
          ],
        },
        {
          id: 2,
          customer_id: 2,
          customer_name: 'محمد بن علي',
          customer_type: 'individual',
          debt_amount: 125000,
          paid_amount: 0,
          remaining_amount: 125000,
          due_date: '2024-05-25',
          status: 'overdue',
          notes: 'دين متأخر',
          created_at: '2024-04-25',
          updated_at: '2024-04-25',
          payments: [],
        },
        {
          id: 3,
          customer_id: 3,
          customer_name: 'ورشة الأمين',
          customer_type: 'workshop',
          debt_amount: 89000,
          paid_amount: 89000,
          remaining_amount: 0,
          due_date: '2024-06-01',
          status: 'fully_paid',
          notes: 'تم السداد كاملاً',
          created_at: '2024-05-05',
          updated_at: '2024-05-25',
          payments: [
            {
              id: 3,
              debt_id: 3,
              amount: 89000,
              payment_date: '2024-05-25',
              payment_method: 'check',
              notes: 'سداد كامل بشيك',
              created_at: '2024-05-25',
            },
          ],
        },
        {
          id: 4,
          customer_id: 4,
          customer_name: 'أسطول النقل الحديث',
          customer_type: 'fleet_owner',
          debt_amount: 780000,
          paid_amount: 300000,
          remaining_amount: 480000,
          due_date: '2024-07-10',
          status: 'partially_paid',
          notes: 'دين كبير - يحتاج متابعة',
          created_at: '2024-05-15',
          updated_at: '2024-05-22',
          payments: [
            {
              id: 4,
              debt_id: 4,
              amount: 300000,
              payment_date: '2024-05-22',
              payment_method: 'bank_transfer',
              notes: 'دفعة جزئية',
              created_at: '2024-05-22',
            },
          ],
        },
        {
          id: 5,
          customer_id: 5,
          customer_name: 'فاطمة الزهراء',
          customer_type: 'individual',
          debt_amount: 25000,
          paid_amount: 0,
          remaining_amount: 25000,
          due_date: '2024-06-30',
          status: 'pending',
          notes: 'دين جديد',
          created_at: '2024-05-28',
          updated_at: '2024-05-28',
          payments: [],
        },
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setDebts(mockDebts);
    } catch (error) {
      console.error('Error loading debts:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedDebt(null);
    setDebtDialogOpen(true);
  };

  const handleEdit = (debt: Debt) => {
    setSelectedDebt(debt);
    setDebtDialogOpen(true);
  };

  const handleRecordPayment = (debt: Debt) => {
    setSelectedDebt(debt);
    setPaymentDialogOpen(true);
  };

  const handleViewHistory = (debt: Debt) => {
    setSelectedDebt(debt);
    setHistoryDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الدين؟')) {
      try {
        setDebts(prev => prev.filter(debt => debt.id !== id));
        setSnackbar({ open: true, message: 'تم حذف الدين بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف الدين', severity: 'error' });
      }
    }
  };

  const handleSaveDebt = async (debtData: Partial<Debt>) => {
    try {
      if (selectedDebt) {
        // تحديث دين موجود
        setDebts(prev => prev.map(debt =>
          debt.id === selectedDebt.id ? {
            ...debt,
            ...debtData,
            updated_at: new Date().toISOString().split('T')[0]
          } : debt
        ));
        setSnackbar({ open: true, message: 'تم تحديث الدين بنجاح', severity: 'success' });
      } else {
        // إضافة دين جديد
        const newDebt: Debt = {
          id: Date.now(),
          customer_id: Date.now(),
          paid_amount: 0,
          remaining_amount: debtData.debt_amount || 0,
          status: 'pending',
          created_at: new Date().toISOString().split('T')[0],
          updated_at: new Date().toISOString().split('T')[0],
          payments: [],
          ...debtData as Debt,
        };
        setDebts(prev => [...prev, newDebt]);
        setSnackbar({ open: true, message: 'تم إضافة الدين بنجاح', severity: 'success' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'خطأ في حفظ الدين', severity: 'error' });
    }
  };

  const handleSavePayment = async (paymentData: Partial<Payment>) => {
    try {
      if (!selectedDebt) return;

      const newPayment: Payment = {
        id: Date.now(),
        created_at: new Date().toISOString().split('T')[0],
        ...paymentData as Payment,
      };

      setDebts(prev => prev.map(debt => {
        if (debt.id === selectedDebt.id) {
          const newPaidAmount = debt.paid_amount + (paymentData.amount || 0);
          const newRemainingAmount = debt.debt_amount - newPaidAmount;
          const newStatus = newRemainingAmount <= 0 ? 'fully_paid' : 'partially_paid';

          return {
            ...debt,
            paid_amount: newPaidAmount,
            remaining_amount: newRemainingAmount,
            status: newStatus,
            updated_at: new Date().toISOString().split('T')[0],
            payments: [...(debt.payments || []), newPayment],
          };
        }
        return debt;
      }));

      setSnackbar({ open: true, message: 'تم تسجيل الدفعة بنجاح', severity: 'success' });
    } catch (error) {
      setSnackbar({ open: true, message: 'خطأ في تسجيل الدفعة', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filteredDebts = debts.filter(debt => {
    const matchesSearch = debt.customer_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || debt.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // حساب الإحصائيات
  const totalDebts = debts.reduce((sum, debt) => sum + debt.debt_amount, 0);
  const totalPaid = debts.reduce((sum, debt) => sum + debt.paid_amount, 0);
  const totalRemaining = debts.reduce((sum, debt) => sum + debt.remaining_amount, 0);
  const overdueDebts = debts.filter(debt =>
    new Date(debt.due_date) < new Date() && debt.status !== 'fully_paid'
  ).length;

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('debts')}
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalDebts.toLocaleString()}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الديون (دج)
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {totalPaid.toLocaleString()}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي المدفوع (دج)
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
              {totalRemaining.toLocaleString()}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي المتبقي (دج)
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {overdueDebts}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              ديون متأخرة
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث في الديون..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب الحالة</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="تصفية حسب الحالة"
              >
                <MenuItem value="">جميع الحالات</MenuItem>
                <MenuItem value="pending">في الانتظار</MenuItem>
                <MenuItem value="overdue">متأخر</MenuItem>
                <MenuItem value="partially_paid">مدفوع جزئياً</MenuItem>
                <MenuItem value="fully_paid">مدفوع بالكامل</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={5} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {t('addDebt')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* جدول البيانات */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredDebts}
          columns={columns}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          sx={{
            '& .MuiDataGrid-root': {
              fontFamily: 'Cairo, sans-serif',
            },
          }}
        />
      </Paper>

      {/* نماذج الحوار */}
      <DebtDialog
        open={debtDialogOpen}
        onClose={() => setDebtDialogOpen(false)}
        debt={selectedDebt}
        onSave={handleSaveDebt}
      />

      <PaymentDialog
        open={paymentDialogOpen}
        onClose={() => setPaymentDialogOpen(false)}
        debt={selectedDebt}
        onSave={handleSavePayment}
      />

      <PaymentHistory
        debt={historyDialogOpen ? selectedDebt : null}
        onClose={() => setHistoryDialogOpen(false)}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Debts;