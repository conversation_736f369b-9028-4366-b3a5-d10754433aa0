import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  AccountBalance as DebtIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';
import CustomerDebtManagement from '../components/Debts/CustomerDebtManagement';

// تعريف أنواع البيانات
interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  customer_type: 'individual' | 'company' | 'workshop' | 'fleet_owner';
  registration_date: string;
  total_purchases: number;
  last_purchase_date: string;
  notes: string;
  is_active: boolean;
  total_debts: number;
  debt_status: 'no_debts' | 'normal_debts' | 'overdue_debts';
  last_payment_date?: string;
  overdue_amount: number;
}

interface CustomerPurchase {
  id: number;
  invoice_number: string;
  date: string;
  amount: number;
  payment_status: string;
}

// مكون نموذج إضافة/تعديل عميل
interface CustomerDialogProps {
  open: boolean;
  onClose: () => void;
  customer?: Customer | null;
  onSave: (customer: Partial<Customer>) => void;
}

const CustomerDialog: React.FC<CustomerDialogProps> = ({ open, onClose, customer, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<Customer>>({
    name: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    customer_type: 'individual',
    notes: '',
    is_active: true,
  });

  useEffect(() => {
    if (customer) {
      setFormData(customer);
    } else {
      setFormData({
        name: '',
        phone: '',
        email: '',
        address: '',
        city: '',
        customer_type: 'individual',
        notes: '',
        is_active: true,
      });
    }
  }, [customer, open]);

  const handleChange = (field: keyof Customer) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {customer ? t('editCustomer') : t('addCustomer')}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('customerName')}
              value={formData.name || ''}
              onChange={handleChange('name')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>{t('customerType')}</InputLabel>
              <Select
                value={formData.customer_type || 'individual'}
                onChange={handleChange('customer_type')}
                label={t('customerType')}
              >
                <MenuItem value="individual">{t('individual')}</MenuItem>
                <MenuItem value="company">{t('company')}</MenuItem>
                <MenuItem value="workshop">{t('workshop')}</MenuItem>
                <MenuItem value="fleet_owner">{t('fleetOwner')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('phone')}
              value={formData.phone || ''}
              onChange={handleChange('phone')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('email')}
              type="email"
              value={formData.email || ''}
              onChange={handleChange('email')}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('address')}
              value={formData.address || ''}
              onChange={handleChange('address')}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('city')}
              value={formData.city || ''}
              onChange={handleChange('city')}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="ملاحظات"
              multiline
              rows={3}
              value={formData.notes || ''}
              onChange={handleChange('notes')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={formData.is_active ? 1 : 0}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 1 }))}
                label="الحالة"
              >
                <MenuItem value={1}>نشط</MenuItem>
                <MenuItem value={0}>غير نشط</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// مكون عرض تفاصيل العميل
interface CustomerDetailsProps {
  customer: Customer;
  purchases: CustomerPurchase[];
}

const CustomerDetails: React.FC<CustomerDetailsProps> = ({ customer, purchases }) => {
  const [tabValue, setTabValue] = useState(0);

  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'individual': return <PersonIcon />;
      case 'company': return <BusinessIcon />;
      case 'workshop': return <BusinessIcon />;
      case 'fleet_owner': return <BusinessIcon />;
      default: return <PersonIcon />;
    }
  };

  const getCustomerTypeText = (type: string) => {
    switch (type) {
      case 'individual': return 'فرد';
      case 'company': return 'شركة';
      case 'workshop': return 'ورشة';
      case 'fleet_owner': return 'مالك أسطول';
      default: return type;
    }
  };

  return (
    <Box>
      <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 2 }}>
        <Tab label="معلومات العميل" sx={{ fontFamily: 'Cairo, sans-serif' }} />
        <Tab label="تاريخ المشتريات" sx={{ fontFamily: 'Cairo, sans-serif' }} />
      </Tabs>

      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
                  المعلومات الأساسية
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {getCustomerTypeIcon(customer.customer_type)}
                  <Typography sx={{ ml: 1, fontFamily: 'Cairo, sans-serif' }}>
                    {customer.name} - {getCustomerTypeText(customer.customer_type)}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <PhoneIcon sx={{ mr: 1, color: '#64748b' }} />
                  <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                    {customer.phone}
                  </Typography>
                </Box>
                {customer.email && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <EmailIcon sx={{ mr: 1, color: '#64748b' }} />
                    <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                      {customer.email}
                    </Typography>
                  </Box>
                )}
                {customer.address && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <LocationIcon sx={{ mr: 1, color: '#64748b' }} />
                    <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                      {customer.address}, {customer.city}
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
                  إحصائيات المشتريات
                </Typography>
                <Typography sx={{ mb: 1, fontFamily: 'Cairo, sans-serif' }}>
                  إجمالي المشتريات: {customer.total_purchases.toLocaleString()} دج
                </Typography>
                <Typography sx={{ mb: 1, fontFamily: 'Cairo, sans-serif' }}>
                  آخر عملية شراء: {new Date(customer.last_purchase_date).toLocaleDateString('ar-DZ')}
                </Typography>
                <Typography sx={{ mb: 1, fontFamily: 'Cairo, sans-serif' }}>
                  تاريخ التسجيل: {new Date(customer.registration_date).toLocaleDateString('ar-DZ')}
                </Typography>
                <Chip
                  label={customer.is_active ? 'نشط' : 'غير نشط'}
                  color={customer.is_active ? 'success' : 'default'}
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>
          {customer.notes && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
                    ملاحظات
                  </Typography>
                  <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                    {customer.notes}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      )}

      {tabValue === 1 && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              تاريخ المشتريات
            </Typography>
            {purchases.length > 0 ? (
              <List>
                {purchases.map((purchase, index) => (
                  <React.Fragment key={purchase.id}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                              فاتورة رقم: {purchase.invoice_number}
                            </Typography>
                            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                              {purchase.amount.toLocaleString()} دج
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                              {new Date(purchase.date).toLocaleDateString('ar-DZ')}
                            </Typography>
                            <Chip
                              label={purchase.payment_status === 'paid' ? 'مدفوع' : 'غير مدفوع'}
                              size="small"
                              color={purchase.payment_status === 'paid' ? 'success' : 'error'}
                            />
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < purchases.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography sx={{ textAlign: 'center', color: '#64748b', fontFamily: 'Cairo, sans-serif' }}>
                لا توجد مشتريات سابقة
              </Typography>
            )}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

const Customers: React.FC = () => {
  const { t } = useTranslation();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [debtStatusFilter, setDebtStatusFilter] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [debtsManagementOpen, setDebtsManagementOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerPurchases, setCustomerPurchases] = useState<CustomerPurchase[]>([]);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم العميل',
      width: 200,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'customer_type',
      headerName: 'النوع',
      width: 120,
      renderCell: (params) => {
        const getTypeText = (type: string) => {
          switch (type) {
            case 'individual': return 'فرد';
            case 'company': return 'شركة';
            case 'workshop': return 'ورشة';
            case 'fleet_owner': return 'مالك أسطول';
            default: return type;
          }
        };

        const getTypeColor = (type: string) => {
          switch (type) {
            case 'individual': return 'default';
            case 'company': return 'primary';
            case 'workshop': return 'secondary';
            case 'fleet_owner': return 'warning';
            default: return 'default';
          }
        };

        return (
          <Chip
            label={getTypeText(params.value)}
            size="small"
            color={getTypeColor(params.value) as any}
          />
        );
      },
    },
    {
      field: 'phone',
      headerName: 'الهاتف',
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'city',
      headerName: 'المدينة',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value || '-'}
        </Typography>
      ),
    },
    {
      field: 'total_purchases',
      headerName: 'إجمالي المشتريات',
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'last_purchase_date',
      headerName: 'آخر شراء',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {new Date(params.value).toLocaleDateString('ar-DZ')}
        </Typography>
      ),
    },
    {
      field: 'total_debts',
      headerName: 'إجمالي الديون',
      width: 130,
      renderCell: (params) => (
        <Typography sx={{
          fontFamily: 'Cairo, sans-serif',
          fontWeight: 'bold',
          color: params.value > 0 ? '#ef4444' : '#10b981'
        }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'debt_status',
      headerName: 'حالة الديون',
      width: 130,
      renderCell: (params) => {
        const getDebtStatusConfig = (status: string) => {
          switch (status) {
            case 'no_debts':
              return { label: 'لا توجد ديون', color: 'success' as const, icon: <CheckIcon /> };
            case 'normal_debts':
              return { label: 'ديون عادية', color: 'warning' as const, icon: <ScheduleIcon /> };
            case 'overdue_debts':
              return { label: 'ديون متأخرة', color: 'error' as const, icon: <WarningIcon /> };
            default:
              return { label: 'غير محدد', color: 'default' as const, icon: <ScheduleIcon /> };
          }
        };

        const config = getDebtStatusConfig(params.value);
        return (
          <Chip
            icon={config.icon}
            label={config.label}
            size="small"
            color={config.color}
          />
        );
      },
    },
    {
      field: 'is_active',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          size="small"
          color={params.value ? 'success' : 'default'}
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleViewDetails(params.row)}
            sx={{ color: '#3b82f6' }}
            title="عرض التفاصيل"
          >
            <HistoryIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleManageDebts(params.row)}
            sx={{ color: '#f59e0b' }}
            title="إدارة الديون"
          >
            <DebtIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleEdit(params.row)}
            sx={{ color: '#10b981' }}
            title="تعديل"
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(params.row.id)}
            sx={{ color: '#ef4444' }}
            title="حذف"
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات - سيتم استبدالها بـ API حقيقي
      const mockCustomers: Customer[] = [
        {
          id: 1,
          name: 'أحمد محمد',
          phone: '0123456789',
          email: '<EMAIL>',
          address: 'شارع الاستقلال 123',
          city: 'الجزائر',
          customer_type: 'individual',
          registration_date: '2024-01-15',
          total_purchases: 125000,
          last_purchase_date: '2024-05-29',
          notes: 'عميل مميز، يشتري بانتظام',
          is_active: true,
          total_debts: 0,
          debt_status: 'no_debts',
          overdue_amount: 0,
        },
        {
          id: 2,
          name: 'شركة النقل السريع',
          phone: '0987654321',
          email: '<EMAIL>',
          address: 'المنطقة الصناعية',
          city: 'وهران',
          customer_type: 'company',
          registration_date: '2023-11-20',
          total_purchases: 450000,
          last_purchase_date: '2024-05-28',
          notes: 'شركة نقل كبيرة، تحتاج قطع غيار بكميات كبيرة',
          is_active: true,
          total_debts: 250000,
          debt_status: 'normal_debts',
          last_payment_date: '2024-05-20',
          overdue_amount: 0,
        },
        {
          id: 3,
          name: 'ورشة الأمين للصيانة',
          phone: '0555123456',
          email: '<EMAIL>',
          address: 'حي الصناعات',
          city: 'قسنطينة',
          customer_type: 'workshop',
          registration_date: '2024-02-10',
          total_purchases: 89000,
          last_purchase_date: '2024-05-25',
          notes: 'ورشة متخصصة في صيانة الشاحنات',
          is_active: true,
          total_debts: 0,
          debt_status: 'no_debts',
          overdue_amount: 0,
        },
        {
          id: 4,
          name: 'محمد بن علي - مالك أسطول',
          phone: '0777888999',
          email: '<EMAIL>',
          address: 'طريق الميناء',
          city: 'عنابة',
          customer_type: 'fleet_owner',
          registration_date: '2023-08-05',
          total_purchases: 320000,
          last_purchase_date: '2024-05-20',
          notes: 'يملك أسطول من 15 شاحنة',
          is_active: true,
          total_debts: 125000,
          debt_status: 'overdue_debts',
          last_payment_date: '2024-04-15',
          overdue_amount: 125000,
        },
        {
          id: 5,
          name: 'فاطمة الزهراء',
          phone: '0666777888',
          email: '<EMAIL>',
          address: 'حي النصر',
          city: 'سطيف',
          customer_type: 'individual',
          registration_date: '2024-03-12',
          total_purchases: 25000,
          last_purchase_date: '2024-04-15',
          notes: '',
          is_active: false,
          total_debts: 25000,
          debt_status: 'normal_debts',
          overdue_amount: 0,
        },
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Error loading customers:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const loadCustomerPurchases = async (customerId: number) => {
    // محاكاة تحميل تاريخ مشتريات العميل
    const mockPurchases: CustomerPurchase[] = [
      {
        id: 1,
        invoice_number: 'INV-001',
        date: '2024-05-29',
        amount: 57500,
        payment_status: 'paid',
      },
      {
        id: 2,
        invoice_number: 'INV-005',
        date: '2024-05-15',
        amount: 32000,
        payment_status: 'paid',
      },
      {
        id: 3,
        invoice_number: 'INV-010',
        date: '2024-04-28',
        amount: 15000,
        payment_status: 'unpaid',
      },
    ];

    setCustomerPurchases(mockPurchases);
  };

  const handleAdd = () => {
    setSelectedCustomer(null);
    setDialogOpen(true);
  };

  const handleEdit = (customer: Customer) => {
    setSelectedCustomer(customer);
    setDialogOpen(true);
  };

  const handleViewDetails = async (customer: Customer) => {
    setSelectedCustomer(customer);
    await loadCustomerPurchases(customer.id);
    setDetailsOpen(true);
  };

  const handleManageDebts = (customer: Customer) => {
    setSelectedCustomer(customer);
    setDebtsManagementOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        // هنا سيتم حذف العميل من قاعدة البيانات
        setCustomers(prev => prev.filter(customer => customer.id !== id));
        setSnackbar({ open: true, message: 'تم حذف العميل بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف العميل', severity: 'error' });
      }
    }
  };

  const handleSave = async (customerData: Partial<Customer>) => {
    try {
      if (selectedCustomer) {
        // تحديث عميل موجود
        setCustomers(prev => prev.map(customer =>
          customer.id === selectedCustomer.id ? { ...customer, ...customerData } : customer
        ));
        setSnackbar({ open: true, message: 'تم تحديث العميل بنجاح', severity: 'success' });
      } else {
        // إضافة عميل جديد
        const newCustomer: Customer = {
          id: Date.now(),
          registration_date: new Date().toISOString().split('T')[0],
          total_purchases: 0,
          last_purchase_date: new Date().toISOString().split('T')[0],
          ...customerData as Customer,
        };
        setCustomers(prev => [...prev, newCustomer]);
        setSnackbar({ open: true, message: 'تم إضافة العميل بنجاح', severity: 'success' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'خطأ في حفظ العميل', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !typeFilter || customer.customer_type === typeFilter;
    const matchesDebtStatus = !debtStatusFilter || customer.debt_status === debtStatusFilter;
    return matchesSearch && matchesType && matchesDebtStatus;
  });

  // حساب الإحصائيات
  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(customer => customer.is_active).length;
  const totalPurchases = customers.reduce((sum, customer) => sum + customer.total_purchases, 0);

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('customers')}
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalCustomers}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي العملاء
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {activeCustomers}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              عملاء نشطون
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {totalPurchases.toLocaleString()}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي المشتريات (دج)
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث في العملاء..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={2.5}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب النوع</InputLabel>
              <Select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                label="تصفية حسب النوع"
              >
                <MenuItem value="">جميع الأنواع</MenuItem>
                <MenuItem value="individual">فرد</MenuItem>
                <MenuItem value="company">شركة</MenuItem>
                <MenuItem value="workshop">ورشة</MenuItem>
                <MenuItem value="fleet_owner">مالك أسطول</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2.5}>
            <FormControl fullWidth>
              <InputLabel>حالة الديون</InputLabel>
              <Select
                value={debtStatusFilter}
                onChange={(e) => setDebtStatusFilter(e.target.value)}
                label="حالة الديون"
              >
                <MenuItem value="">جميع الحالات</MenuItem>
                <MenuItem value="no_debts">لا توجد ديون</MenuItem>
                <MenuItem value="normal_debts">ديون عادية</MenuItem>
                <MenuItem value="overdue_debts">ديون متأخرة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {t('addCustomer')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* جدول البيانات */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredCustomers}
          columns={columns}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          sx={{
            '& .MuiDataGrid-root': {
              fontFamily: 'Cairo, sans-serif',
            },
          }}
        />
      </Paper>

      {/* نموذج إضافة/تعديل */}
      <CustomerDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        customer={selectedCustomer}
        onSave={handleSave}
      />

      {/* نافذة تفاصيل العميل */}
      <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
          تفاصيل العميل: {selectedCustomer?.name}
        </DialogTitle>
        <DialogContent>
          {selectedCustomer && (
            <CustomerDetails customer={selectedCustomer} purchases={customerPurchases} />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)} sx={{ fontFamily: 'Cairo, sans-serif' }}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إدارة ديون العميل */}
      <CustomerDebtManagement
        open={debtsManagementOpen}
        onClose={() => setDebtsManagementOpen(false)}
        customer={selectedCustomer}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Customers;