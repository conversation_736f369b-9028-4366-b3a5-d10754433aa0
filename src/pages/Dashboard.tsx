import React, { useEffect, useState } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  ShoppingCart as SalesIcon,
  People as CustomersIcon,
  LocalShipping as SuppliersIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Receipt as ReceiptIcon,
  ShoppingCart as ShoppingCartIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNotifications } from '../contexts/NotificationContext';

// مكون بطاقة الإحصائيات
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend }) => {
  return (
    <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: color, mb: 1 }}>
              {value}
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', fontFamily: 'Cairo, sans-serif' }}>
              {title}
            </Typography>
            {trend && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <TrendingUpIcon
                  sx={{
                    fontSize: 16,
                    color: trend.isPositive ? '#10b981' : '#ef4444',
                    transform: trend.isPositive ? 'none' : 'rotate(180deg)',
                  }}
                />
                <Typography
                  variant="caption"
                  sx={{
                    color: trend.isPositive ? '#10b981' : '#ef4444',
                    ml: 0.5,
                  }}
                >
                  {trend.value}%
                </Typography>
              </Box>
            )}
          </Box>
          <Box
            sx={{
              backgroundColor: `${color}20`,
              borderRadius: 2,
              p: 1.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// مكون قائمة التنبيهات
const AlertsList: React.FC = () => {
  const { t } = useTranslation();

  const alerts = [
    { id: 1, message: 'قطعة رقم 12345 وصلت للحد الأدنى', type: 'warning', time: '10 دقائق' },
    { id: 2, message: 'فاتورة العميل أحمد محمد مستحقة', type: 'error', time: '30 دقيقة' },
    { id: 3, message: 'وصول شحنة جديدة من المورد', type: 'info', time: '1 ساعة' },
    { id: 4, message: 'تم إنجاز عملية بيع بقيمة 15000 دج', type: 'success', time: '2 ساعة' },
  ];

  const getChipColor = (type: string) => {
    switch (type) {
      case 'warning': return 'warning';
      case 'error': return 'error';
      case 'success': return 'success';
      default: return 'info';
    }
  };

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
        التنبيهات الأخيرة
      </Typography>
      <List>
        {alerts.map((alert) => (
          <ListItem key={alert.id} sx={{ px: 0 }}>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                    {alert.message}
                  </Typography>
                  <Chip
                    label={alert.type}
                    size="small"
                    color={getChipColor(alert.type) as any}
                  />
                </Box>
              }
              secondary={
                <Typography variant="caption" sx={{ color: '#64748b' }}>
                  منذ {alert.time}
                </Typography>
              }
            />
          </ListItem>
        ))}
      </List>
    </Paper>
  );
};

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { addNotification } = useNotifications();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalParts: 0,
    totalSales: 0,
    totalCustomers: 0,
    totalSuppliers: 0,
    lowStockItems: 0,
    todaySales: 0,
    pendingPurchaseOrders: 0,
    totalPurchaseValue: 0,
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    const loadDashboardData = async () => {
      try {
        // هنا سيتم استدعاء API لجلب البيانات الفعلية
        await new Promise(resolve => setTimeout(resolve, 1000));

        setStats({
          totalParts: 1250,
          totalSales: 45000,
          totalCustomers: 180,
          totalSuppliers: 25,
          lowStockItems: 8,
          todaySales: 3200,
          pendingPurchaseOrders: 4,
          totalPurchaseValue: 1384000,
        });

        // إضافة تنبيه ترحيبي
        addNotification({
          type: 'system',
          title: 'مرحباً بك',
          message: 'تم تحميل لوحة التحكم بنجاح',
          priority: 'low',
        });
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [addNotification]);

  if (loading) {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <LinearProgress />
        <Typography sx={{ textAlign: 'center', mt: 2, fontFamily: 'Cairo, sans-serif' }}>
          جاري تحميل البيانات...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        لوحة التحكم
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي قطع الغيار"
            value={stats.totalParts.toLocaleString()}
            icon={<InventoryIcon sx={{ color: '#3b82f6', fontSize: 28 }} />}
            color="#3b82f6"
            trend={{ value: 12, isPositive: true }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="مبيعات اليوم (دج)"
            value={stats.todaySales.toLocaleString()}
            icon={<SalesIcon sx={{ color: '#10b981', fontSize: 28 }} />}
            color="#10b981"
            trend={{ value: 8, isPositive: true }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي العملاء"
            value={stats.totalCustomers}
            icon={<CustomersIcon sx={{ color: '#8b5cf6', fontSize: 28 }} />}
            color="#8b5cf6"
            trend={{ value: 5, isPositive: true }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الموردين"
            value={stats.totalSuppliers}
            icon={<SuppliersIcon sx={{ color: '#f59e0b', fontSize: 28 }} />}
            color="#f59e0b"
          />
        </Grid>
      </Grid>

      {/* بطاقات إحصائيات طلبيات الشراء */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={6}>
          <StatCard
            title="طلبيات الشراء المعلقة"
            value={stats.pendingPurchaseOrders}
            icon={<ReceiptIcon sx={{ color: '#ef4444', fontSize: 28 }} />}
            color="#ef4444"
            trend={{ value: 2, isPositive: false }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <StatCard
            title="قيمة طلبيات الشراء (دج)"
            value={stats.totalPurchaseValue.toLocaleString()}
            icon={<ShoppingCartIcon sx={{ color: '#6366f1', fontSize: 28 }} />}
            color="#6366f1"
            trend={{ value: 15, isPositive: true }}
          />
        </Grid>
      </Grid>

      {/* تنبيه المخزون المنخفض */}
      {stats.lowStockItems > 0 && (
        <Card sx={{ mb: 3, backgroundColor: '#fef3c7', borderLeft: '4px solid #f59e0b' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <WarningIcon sx={{ color: '#f59e0b', mr: 2 }} />
              <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                تحذير: يوجد {stats.lowStockItems} قطع وصلت للحد الأدنى من المخزون
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* التنبيهات والأنشطة الأخيرة */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <AlertsList />
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              المبيعات الأخيرة
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', fontFamily: 'Cairo, sans-serif' }}>
              سيتم عرض المبيعات الأخيرة هنا...
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
