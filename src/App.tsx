import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

// المكونات
import Layout from './components/Layout/Layout';

// الصفحات
import Dashboard from './pages/Dashboard';
import Inventory from './pages/Inventory';
import Sales from './pages/Sales';
import Customers from './pages/Customers';
import Suppliers from './pages/Suppliers';
import TruckModels from './pages/TruckModels';
import Categories from './pages/Categories';
import Compatibility from './pages/Compatibility';
import Debts from './pages/Debts';
import Reports from './pages/Reports';
import Settings from './pages/Settings';

// إنشاء ثيم مخصص للعربية
const createArabicTheme = () => {
  return createTheme({
    direction: 'rtl',
    typography: {
      fontFamily: [
        'Cairo',
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        'Roboto',
        '"Helvetica Neue"',
        'Arial',
        'sans-serif',
      ].join(','),
    },
    palette: {
      primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
      },
      secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
      },
      background: {
        default: '#f5f5f5',
        paper: '#ffffff',
      },
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            direction: 'rtl',
            fontFamily: 'Cairo, sans-serif',
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            fontFamily: 'Cairo, sans-serif',
            fontWeight: 500,
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiInputBase-input': {
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl',
              textAlign: 'right',
            },
          },
        },
      },
      MuiDataGrid: {
        styleOverrides: {
          root: {
            '& .MuiDataGrid-cell': {
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl',
              textAlign: 'right',
            },
            '& .MuiDataGrid-columnHeader': {
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl',
              textAlign: 'right',
            },
          },
        },
      },
    },
  });
};

function App() {
  const { i18n } = useTranslation();
  const theme = createArabicTheme();

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/inventory" element={<Inventory />} />
            <Route path="/sales" element={<Sales />} />
            <Route path="/customers" element={<Customers />} />
            <Route path="/suppliers" element={<Suppliers />} />
            <Route path="/truck-models" element={<TruckModels />} />
            <Route path="/categories" element={<Categories />} />
            <Route path="/compatibility" element={<Compatibility />} />
            <Route path="/debts" element={<Debts />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
