import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

// المكونات
import Sidebar from './components/Layout/Sidebar';
import Header from './components/Layout/Header';

// الصفحات
import Dashboard from './pages/Dashboard';
import Inventory from './pages/Inventory';
import Sales from './pages/Sales';
import Customers from './pages/Customers';
import Suppliers from './pages/Suppliers';
import Reports from './pages/Reports';
import Settings from './pages/Settings';

// إنشاء ثيم مخصص للعربية
const createArabicTheme = () => {
  return createTheme({
    direction: 'rtl',
    typography: {
      fontFamily: [
        'Cairo',
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        'Roboto',
        '"Helvetica Neue"',
        'Arial',
        'sans-serif',
      ].join(','),
    },
    palette: {
      primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
      },
      secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
      },
      background: {
        default: '#f5f5f5',
        paper: '#ffffff',
      },
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            direction: 'rtl',
            fontFamily: 'Cairo, sans-serif',
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            fontFamily: 'Cairo, sans-serif',
            fontWeight: 500,
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiInputBase-input': {
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl',
              textAlign: 'right',
            },
          },
        },
      },
      MuiDataGrid: {
        styleOverrides: {
          root: {
            '& .MuiDataGrid-cell': {
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl',
              textAlign: 'right',
            },
            '& .MuiDataGrid-columnHeader': {
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl',
              textAlign: 'right',
            },
          },
        },
      },
    },
  });
};

function App() {
  const { i18n } = useTranslation();
  const theme = createArabicTheme();

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ display: 'flex', height: '100vh', direction: 'rtl' }}>
          {/* المحتوى الرئيسي */}
          <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', direction: 'ltr' }}>
            {/* الرأس */}
            <Header />

            {/* المحتوى */}
            <Box sx={{ flexGrow: 1, overflow: 'auto', p: 3 }}>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/inventory" element={<Inventory />} />
                <Route path="/sales" element={<Sales />} />
                <Route path="/customers" element={<Customers />} />
                <Route path="/suppliers" element={<Suppliers />} />
                <Route path="/reports" element={<Reports />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </Box>
          </Box>

          {/* الشريط الجانبي */}
          <Sidebar />
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
