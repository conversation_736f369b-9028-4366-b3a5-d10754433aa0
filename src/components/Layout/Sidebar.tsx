import React, { useState, useEffect } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Inventory as InventoryIcon,
  ShoppingCart as SalesIcon,
  People as CustomersIcon,
  LocalShipping as SuppliersIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  DirectionsCar as TruckIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const drawerWidthExpanded = 280;
const drawerWidthCollapsed = 80;

const menuItems = [
  { key: 'dashboard', path: '/dashboard', icon: DashboardIcon },
  { key: 'inventory', path: '/inventory', icon: InventoryIcon },
  { key: 'sales', path: '/sales', icon: SalesIcon },
  { key: 'customers', path: '/customers', icon: CustomersIcon },
  { key: 'suppliers', path: '/suppliers', icon: SuppliersIcon },
  { key: 'reports', path: '/reports', icon: ReportsIcon },
  { key: 'settings', path: '/settings', icon: SettingsIcon },
];

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const drawerWidth = isCollapsed ? drawerWidthCollapsed : drawerWidthExpanded;

  return (
    <Drawer
      variant="permanent"
      anchor="right"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          backgroundColor: '#1e293b',
          color: 'white',
          right: 0,
          transition: 'width 0.3s ease-in-out',
          borderLeft: '1px solid #334155',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: '#0f172a',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#475569',
            borderRadius: '3px',
            '&:hover': {
              backgroundColor: '#64748b',
            },
          },
        },
      }}
    >
      {/* شعار التطبيق وزر التبديل */}
      <Box
        sx={{
          p: isCollapsed ? 1 : 3,
          display: 'flex',
          alignItems: 'center',
          justifyContent: isCollapsed ? 'center' : 'space-between',
          backgroundColor: '#0f172a',
          minHeight: 80,
        }}
      >
        {!isCollapsed && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TruckIcon sx={{ fontSize: 32, mr: 1, color: '#3b82f6' }} />
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                إدارة قطع الغيار
              </Typography>
              <Typography variant="caption" sx={{ color: '#94a3b8' }}>
                Truck Parts Manager
              </Typography>
            </Box>
          </Box>
        )}

        {isCollapsed && (
          <TruckIcon sx={{ fontSize: 32, color: '#3b82f6' }} />
        )}

        {/* زر التبديل */}
        <Tooltip title={isCollapsed ? 'توسيع الشريط الجانبي' : 'تقليص الشريط الجانبي'}>
          <IconButton
            onClick={onToggle}
            sx={{
              color: 'white',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
              },
              ...(isCollapsed && { mt: 2 }),
            }}
          >
            {isCollapsed ? <ChevronLeftIcon /> : <ChevronRightIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      <Divider sx={{ borderColor: '#334155' }} />

      {/* قائمة التنقل */}
      <List sx={{ pt: 2 }}>
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;

          return (
            <ListItem key={item.key} disablePadding sx={{ px: isCollapsed ? 1 : 2, mb: 1 }}>
              <Tooltip
                title={isCollapsed ? t(item.key) : ''}
                placement="left"
                arrow
              >
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: isActive ? '#3b82f6' : 'transparent',
                    color: isActive ? 'white' : '#cbd5e1',
                    '&:hover': {
                      backgroundColor: isActive ? '#2563eb' : '#334155',
                    },
                    transition: 'all 0.2s ease-in-out',
                    justifyContent: isCollapsed ? 'center' : 'flex-start',
                    px: isCollapsed ? 1 : 2,
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: isActive ? 'white' : '#94a3b8',
                      minWidth: isCollapsed ? 'auto' : 40,
                      justifyContent: 'center',
                    }}
                  >
                    <Icon />
                  </ListItemIcon>
                  {!isCollapsed && (
                    <ListItemText
                      primary={t(item.key)}
                      sx={{
                        '& .MuiListItemText-primary': {
                          fontFamily: 'Cairo, sans-serif',
                          fontWeight: isActive ? 600 : 400,
                        },
                      }}
                    />
                  )}
                </ListItemButton>
              </Tooltip>
            </ListItem>
          );
        })}
      </List>

      {/* معلومات إضافية في أسفل الشريط الجانبي */}
      <Box sx={{ mt: 'auto', p: isCollapsed ? 1 : 2 }}>
        <Divider sx={{ borderColor: '#334155', mb: 2 }} />
        {!isCollapsed && (
          <Typography
            variant="caption"
            sx={{
              color: '#64748b',
              textAlign: 'center',
              display: 'block',
              fontFamily: 'Cairo, sans-serif',
            }}
          >
            الإصدار 1.0.0
          </Typography>
        )}
      </Box>
    </Drawer>
  );
};

export default Sidebar;
