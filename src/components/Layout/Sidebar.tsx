import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Inventory as InventoryIcon,
  ShoppingCart as SalesIcon,
  People as CustomersIcon,
  LocalShipping as SuppliersIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  DirectionsCar as TruckIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const drawerWidth = 280;

const menuItems = [
  { key: 'dashboard', path: '/dashboard', icon: DashboardIcon },
  { key: 'inventory', path: '/inventory', icon: InventoryIcon },
  { key: 'sales', path: '/sales', icon: SalesIcon },
  { key: 'customers', path: '/customers', icon: CustomersIcon },
  { key: 'suppliers', path: '/suppliers', icon: SuppliersIcon },
  { key: 'reports', path: '/reports', icon: ReportsIcon },
  { key: 'settings', path: '/settings', icon: SettingsIcon },
];

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <Drawer
      variant="permanent"
      anchor="right"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          backgroundColor: '#1e293b',
          color: 'white',
          right: 0,
        },
      }}
    >
      {/* شعار التطبيق */}
      <Box
        sx={{
          p: 3,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#0f172a',
        }}
      >
        <TruckIcon sx={{ fontSize: 32, mr: 1, color: '#3b82f6' }} />
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
            إدارة قطع الغيار
          </Typography>
          <Typography variant="caption" sx={{ color: '#94a3b8' }}>
            Truck Parts Manager
          </Typography>
        </Box>
      </Box>

      <Divider sx={{ borderColor: '#334155' }} />

      {/* قائمة التنقل */}
      <List sx={{ pt: 2 }}>
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;

          return (
            <ListItem key={item.key} disablePadding sx={{ px: 2, mb: 1 }}>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                sx={{
                  borderRadius: 2,
                  backgroundColor: isActive ? '#3b82f6' : 'transparent',
                  color: isActive ? 'white' : '#cbd5e1',
                  '&:hover': {
                    backgroundColor: isActive ? '#2563eb' : '#334155',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? 'white' : '#94a3b8',
                    minWidth: 40,
                  }}
                >
                  <Icon />
                </ListItemIcon>
                <ListItemText
                  primary={t(item.key)}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontFamily: 'Cairo, sans-serif',
                      fontWeight: isActive ? 600 : 400,
                    },
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      {/* معلومات إضافية في أسفل الشريط الجانبي */}
      <Box sx={{ mt: 'auto', p: 2 }}>
        <Divider sx={{ borderColor: '#334155', mb: 2 }} />
        <Typography
          variant="caption"
          sx={{
            color: '#64748b',
            textAlign: 'center',
            display: 'block',
            fontFamily: 'Cairo, sans-serif',
          }}
        >
          الإصدار 1.0.0
        </Typography>
      </Box>
    </Drawer>
  );
};

export default Sidebar;
