import React, { useState } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Language as LanguageIcon,
  Backup as BackupIcon,
  Refresh as RefreshIcon,
  Menu as MenuIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';

interface HeaderProps {
  onSidebarToggle?: () => void;
  isSidebarCollapsed?: boolean;
}

const Header: React.FC<HeaderProps> = ({ onSidebarToggle, isSidebarCollapsed }) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [langAnchorEl, setLangAnchorEl] = useState<null | HTMLElement>(null);

  // دالة للحصول على عنوان الصفحة الحالية
  const getPageTitle = () => {
    const path = location.pathname;
    switch (path) {
      case '/':
      case '/dashboard':
        return t('dashboard');
      case '/inventory':
        return t('inventory');
      case '/sales':
        return t('sales');
      case '/customers':
        return t('customers');
      case '/suppliers':
        return t('suppliers');
      case '/reports':
        return t('reports');
      case '/settings':
        return t('settings');
      default:
        return t('dashboard');
    }
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setLangAnchorEl(event.currentTarget);
  };

  const handleLanguageMenuClose = () => {
    setLangAnchorEl(null);
  };

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    handleLanguageMenuClose();
  };

  const handleBackup = () => {
    // سيتم تنفيذ منطق النسخ الاحتياطي هنا
    console.log('Creating backup...');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <AppBar
      position="static"
      sx={{
        backgroundColor: 'white',
        color: '#1e293b',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        borderBottom: '1px solid #e2e8f0',
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* الجانب الأيسر - زر تبديل الشريط الجانبي وعنوان الصفحة */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* زر تبديل الشريط الجانبي */}
          {onSidebarToggle && (
            <Tooltip title={isSidebarCollapsed ? 'توسيع الشريط الجانبي' : 'تقليص الشريط الجانبي'}>
              <IconButton
                onClick={onSidebarToggle}
                sx={{
                  color: '#64748b',
                  '&:hover': {
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    color: '#3b82f6',
                  },
                }}
              >
                <MenuIcon />
              </IconButton>
            </Tooltip>
          )}

          {/* عنوان الصفحة */}
          <Typography
            variant="h6"
            sx={{
              fontFamily: 'Cairo, sans-serif',
              fontWeight: 600,
              color: '#1e293b',
            }}
          >
            {getPageTitle()}
          </Typography>
        </Box>

        {/* أدوات الرأس */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* زر التحديث */}
          <Tooltip title="تحديث">
            <IconButton onClick={handleRefresh} sx={{ color: '#64748b' }}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          {/* زر النسخ الاحتياطي */}
          <Tooltip title="نسخة احتياطية">
            <IconButton onClick={handleBackup} sx={{ color: '#64748b' }}>
              <BackupIcon />
            </IconButton>
          </Tooltip>

          {/* زر اللغة */}
          <Tooltip title="تغيير اللغة">
            <IconButton onClick={handleLanguageMenuOpen} sx={{ color: '#64748b' }}>
              <LanguageIcon />
            </IconButton>
          </Tooltip>

          {/* التنبيهات */}
          <Tooltip title="التنبيهات">
            <IconButton sx={{ color: '#64748b' }}>
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* ملف المستخدم */}
          <Tooltip title="ملف المستخدم">
            <IconButton onClick={handleProfileMenuOpen} sx={{ color: '#64748b' }}>
              <Avatar sx={{ width: 32, height: 32, backgroundColor: '#3b82f6' }}>
                <AccountIcon />
              </Avatar>
            </IconButton>
          </Tooltip>
        </Box>

        {/* قائمة ملف المستخدم */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleProfileMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem onClick={handleProfileMenuClose}>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              الملف الشخصي
            </Typography>
          </MenuItem>
          <MenuItem onClick={handleProfileMenuClose}>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              الإعدادات
            </Typography>
          </MenuItem>
          <MenuItem onClick={handleProfileMenuClose}>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              تسجيل الخروج
            </Typography>
          </MenuItem>
        </Menu>

        {/* قائمة اللغة */}
        <Menu
          anchorEl={langAnchorEl}
          open={Boolean(langAnchorEl)}
          onClose={handleLanguageMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem onClick={() => changeLanguage('ar')}>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              العربية
            </Typography>
          </MenuItem>
          <MenuItem onClick={() => changeLanguage('en')}>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              English
            </Typography>
          </MenuItem>
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
