import React, { useState, useEffect } from 'react';
import { Box, useMediaQuery, useTheme } from '@mui/material';
import Sidebar from './Sidebar';
import Header from './Header';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // إدارة حالة الشريط الجانبي مع حفظها في localStorage
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    // في الشاشات الصغيرة، يكون الشريط الجانبي مقلص افتراضياً
    return saved ? JSON.parse(saved) : isMobile;
  });

  // تحديث حالة الشريط الجانبي عند تغيير حجم الشاشة
  useEffect(() => {
    if (isMobile && !isSidebarCollapsed) {
      setIsSidebarCollapsed(true);
    }
  }, [isMobile, isSidebarCollapsed]);

  // حفظ حالة الشريط الجانبي في localStorage عند تغييرها
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isSidebarCollapsed));
  }, [isSidebarCollapsed]);

  const handleSidebarToggle = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  // حساب عرض الشريط الجانبي
  const sidebarWidth = isSidebarCollapsed ? 80 : 280;

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          marginRight: `${sidebarWidth}px`, // مساحة للشريط الجانبي
          transition: 'margin-right 0.3s ease-in-out', // انتقال سلس
          minHeight: '100vh',
        }}
      >
        {/* رأس الصفحة */}
        <Header
          onSidebarToggle={handleSidebarToggle}
          isSidebarCollapsed={isSidebarCollapsed}
        />

        {/* محتوى الصفحة */}
        <Box
          sx={{
            flexGrow: 1,
            p: 3,
            backgroundColor: '#f8fafc',
            overflow: 'auto',
          }}
        >
          {children}
        </Box>
      </Box>

      {/* الشريط الجانبي */}
      <Sidebar
        isCollapsed={isSidebarCollapsed}
        onToggle={handleSidebarToggle}
      />
    </Box>
  );
};

export default Layout;
