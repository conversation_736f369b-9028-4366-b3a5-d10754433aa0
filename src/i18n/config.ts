import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// ملفات الترجمة
const resources = {
  ar: {
    translation: {
      // القائمة الرئيسية
      dashboard: 'لوحة التحكم',
      inventory: 'المخزون',
      sales: 'المبيعات',
      customers: 'العملاء',
      suppliers: 'الموردون',
      truckModels: 'موديلات الشاحنات',
      categories: 'فئات قطع الغيار',
      compatibility: 'توافق القطع',
      debts: 'إدارة الديون',
      reports: 'التقارير',
      settings: 'الإعدادات',

      // المخزون
      parts: 'قطع الغيار',
      addPart: 'إضافة قطعة',
      editPart: 'تعديل قطعة',
      deletePart: 'حذف قطعة',
      partNumber: 'رقم القطعة',
      partName: 'اسم القطعة',
      category: 'الفئة',
      quantity: 'الكمية',
      price: 'السعر',
      purchasePrice: 'سعر الشراء',
      sellingPrice: 'سعر البيع',
      minQuantity: 'الحد الأدنى',
      location: 'الموقع',

      // المبيعات
      newSale: 'بيع جديد',
      invoice: 'فاتورة',
      invoiceNumber: 'رقم الفاتورة',
      invoiceDate: 'تاريخ الفاتورة',
      customer: 'العميل',
      total: 'الإجمالي',
      discount: 'الخصم',
      tax: 'الضريبة',
      finalAmount: 'المبلغ النهائي',
      paymentStatus: 'حالة الدفع',
      paid: 'مدفوع',
      unpaid: 'غير مدفوع',
      partial: 'جزئي',

      // العملاء
      addCustomer: 'إضافة عميل',
      editCustomer: 'تعديل عميل',
      customerName: 'اسم العميل',
      phone: 'الهاتف',
      email: 'البريد الإلكتروني',
      address: 'العنوان',
      city: 'المدينة',
      country: 'الدولة',
      customerType: 'نوع العميل',
      individual: 'فرد',
      company: 'شركة',
      workshop: 'ورشة',
      fleetOwner: 'مالك أسطول',

      // الموردون
      addSupplier: 'إضافة مورد',
      editSupplier: 'تعديل مورد',
      supplierName: 'اسم المورد',
      contactPerson: 'شخص الاتصال',
      rating: 'التقييم',
      leadTime: 'مهلة التوريد',
      paymentTerms: 'شروط الدفع',

      // موديلات الشاحنات
      addTruckModel: 'إضافة موديل',
      editTruckModel: 'تعديل موديل',
      modelName: 'اسم الموديل',
      manufacturer: 'الشركة المصنعة',
      productionYear: 'سنة الإنتاج',
      engineType: 'نوع المحرك',
      engineCapacity: 'سعة المحرك',
      fuelType: 'نوع الوقود',
      transmission: 'ناقل الحركة',

      // فئات قطع الغيار
      addCategory: 'إضافة فئة',
      editCategory: 'تعديل فئة',
      categoryName: 'اسم الفئة',
      parentCategory: 'الفئة الرئيسية',
      description: 'الوصف',
      image: 'الصورة',
      subcategories: 'الفئات الفرعية',

      // توافق القطع
      partCompatibility: 'توافق القطع',
      compatibleModels: 'الموديلات المتوافقة',
      compatibleParts: 'القطع المتوافقة',
      addCompatibility: 'إضافة توافق',
      removeCompatibility: 'إزالة التوافق',

      // إدارة الديون
      addDebt: 'إضافة دين',
      editDebt: 'تعديل دين',
      debtAmount: 'مبلغ الدين',
      remainingAmount: 'المبلغ المتبقي',
      dueDate: 'تاريخ الاستحقاق',
      debtStatus: 'حالة الدين',
      overdue: 'متأخر',
      partiallyPaid: 'مدفوع جزئياً',
      fullyPaid: 'مدفوع بالكامل',
      paymentHistory: 'تاريخ الدفعات',
      recordPayment: 'تسجيل دفعة',
      paymentAmount: 'مبلغ الدفعة',
      paymentDate: 'تاريخ الدفعة',
      paymentMethod: 'طريقة الدفع',
      cash: 'نقدي',
      bankTransfer: 'تحويل بنكي',
      check: 'شيك',
      notes: 'ملاحظات',

      // التنبيهات
      notifications: 'التنبيهات',
      notification: 'تنبيه',
      unreadNotifications: 'تنبيهات غير مقروءة',
      markAsRead: 'وضع علامة مقروء',
      markAllAsRead: 'وضع علامة مقروء على الكل',
      deleteNotification: 'حذف التنبيه',
      clearAllNotifications: 'حذف جميع التنبيهات',
      notificationSound: 'صوت التنبيه',
      lowStock: 'مخزون منخفض',
      overdueDebt: 'دين متأخر',
      pendingInvoice: 'فاتورة معلقة',
      systemNotification: 'تنبيه النظام',
      highPriority: 'أولوية عالية',
      mediumPriority: 'أولوية متوسطة',
      lowPriority: 'أولوية منخفضة',
      darkMode: 'الوضع الداكن',
      lightMode: 'الوضع الفاتح',
      themeToggle: 'تبديل المظهر',

      // العمليات العامة
      add: 'إضافة',
      edit: 'تعديل',
      delete: 'حذف',
      save: 'حفظ',
      cancel: 'إلغاء',
      search: 'بحث',
      filter: 'تصفية',
      export: 'تصدير',
      print: 'طباعة',
      refresh: 'تحديث',

      // الرسائل
      success: 'تم بنجاح',
      error: 'حدث خطأ',
      warning: 'تحذير',
      info: 'معلومات',
      confirmDelete: 'هل أنت متأكد من الحذف؟',
      noData: 'لا توجد بيانات',
      loading: 'جاري التحميل...',

      // التقارير
      salesReport: 'تقرير المبيعات',
      inventoryReport: 'تقرير المخزون',
      customerReport: 'تقرير العملاء',
      supplierReport: 'تقرير الموردين',
      financialSummary: 'الملخص المالي',

      // الإعدادات
      generalSettings: 'الإعدادات العامة',
      userManagement: 'إدارة المستخدمين',
      backup: 'النسخ الاحتياطي',
      restore: 'الاستعادة',

      // التواريخ والأوقات
      today: 'اليوم',
      yesterday: 'أمس',
      thisWeek: 'هذا الأسبوع',
      thisMonth: 'هذا الشهر',
      thisYear: 'هذا العام',

      // الحالات
      active: 'نشط',
      inactive: 'غير نشط',
      pending: 'في الانتظار',
      completed: 'مكتمل',
      cancelled: 'ملغي',

      // الوحدات
      piece: 'قطعة',
      kg: 'كيلوجرام',
      meter: 'متر',
      liter: 'لتر',

      // العملات
      currency: 'العملة',
      dzd: 'دينار جزائري',
      usd: 'دولار أمريكي',
      eur: 'يورو',
    }
  },
  en: {
    translation: {
      // Main menu
      dashboard: 'Dashboard',
      inventory: 'Inventory',
      sales: 'Sales',
      customers: 'Customers',
      suppliers: 'Suppliers',
      reports: 'Reports',
      settings: 'Settings',

      // Inventory
      parts: 'Parts',
      addPart: 'Add Part',
      editPart: 'Edit Part',
      deletePart: 'Delete Part',
      partNumber: 'Part Number',
      partName: 'Part Name',
      category: 'Category',
      quantity: 'Quantity',
      price: 'Price',
      purchasePrice: 'Purchase Price',
      sellingPrice: 'Selling Price',
      minQuantity: 'Min Quantity',
      location: 'Location',

      // Sales
      newSale: 'New Sale',
      invoice: 'Invoice',
      invoiceNumber: 'Invoice Number',
      invoiceDate: 'Invoice Date',
      customer: 'Customer',
      total: 'Total',
      discount: 'Discount',
      tax: 'Tax',
      finalAmount: 'Final Amount',
      paymentStatus: 'Payment Status',
      paid: 'Paid',
      unpaid: 'Unpaid',
      partial: 'Partial',

      // Common operations
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      search: 'Search',
      filter: 'Filter',
      export: 'Export',
      print: 'Print',
      refresh: 'Refresh',
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'ar', // اللغة الافتراضية
    fallbackLng: 'ar',

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    react: {
      useSuspense: false,
    },
  });

export default i18n;
