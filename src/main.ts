import { app, BrowserWindow, Menu, ipcMain, dialog } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import Database from 'better-sqlite3';

// إعداد قاعدة البيانات
let db: Database.Database;

function createWindow(): void {
  // إنشاء نافذة المتصفح
  const mainWindow = new BrowserWindow({
    height: 800,
    width: 1200,
    minHeight: 600,
    minWidth: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      sandbox: false,
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'إدارة متجر قطع غيار الشاحنات - Truck Parts Manager',
    show: false, // لا تظهر النافذة حتى تكون جاهزة
  });

  // تحميل التطبيق
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // إظهار النافذة عندما تكون جاهزة
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إعداد القائمة العربية
  createArabicMenu();
}

function createArabicMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // إضافة منطق إنشاء عنصر جديد
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // إضافة منطق فتح ملف
          }
        },
        { type: 'separator' },
        {
          label: 'نسخة احتياطية',
          click: () => {
            createBackup();
          }
        },
        {
          label: 'استعادة',
          click: () => {
            restoreBackup();
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            dialog.showMessageBox({
              type: 'info',
              title: 'حول التطبيق',
              message: 'إدارة متجر قطع غيار الشاحنات',
              detail: 'تطبيق شامل لإدارة المخزون والمبيعات والعملاء والموردين\nالإصدار 1.0.0'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// إعداد قاعدة البيانات
function initializeDatabase(): void {
  try {
    const dbPath = path.join(__dirname, '../database/truck_parts.db');

    // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // فتح قاعدة البيانات
    db = new Database(dbPath);

    // تفعيل Foreign Keys
    db.pragma('foreign_keys = ON');

    // قراءة وتنفيذ schema إذا كانت قاعدة البيانات فارغة
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
    if (tables.length === 0) {
      const schemaPath = path.join(__dirname, '../database/database_schema_enhanced_sqlite.sql');
      if (fs.existsSync(schemaPath)) {
        const schema = fs.readFileSync(schemaPath, 'utf8');
        db.exec(schema);
        console.log('Database schema created successfully');
      }
    }

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// إنشاء نسخة احتياطية
async function createBackup(): Promise<void> {
  try {
    const result = await dialog.showSaveDialog({
      title: 'حفظ نسخة احتياطية',
      defaultPath: `backup_${new Date().toISOString().split('T')[0]}.db`,
      filters: [
        { name: 'قاعدة بيانات', extensions: ['db'] },
        { name: 'جميع الملفات', extensions: ['*'] }
      ]
    });

    if (!result.canceled && result.filePath) {
      const sourcePath = path.join(__dirname, '../database/truck_parts.db');
      fs.copyFileSync(sourcePath, result.filePath);

      dialog.showMessageBox({
        type: 'info',
        title: 'نجح الحفظ',
        message: 'تم إنشاء النسخة الاحتياطية بنجاح'
      });
    }
  } catch (error) {
    dialog.showErrorBox('خطأ', 'فشل في إنشاء النسخة الاحتياطية');
  }
}

// استعادة نسخة احتياطية
async function restoreBackup(): Promise<void> {
  try {
    const result = await dialog.showOpenDialog({
      title: 'اختيار نسخة احتياطية',
      filters: [
        { name: 'قاعدة بيانات', extensions: ['db'] },
        { name: 'جميع الملفات', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
      const targetPath = path.join(__dirname, '../database/truck_parts.db');

      // إغلاق قاعدة البيانات الحالية (مؤقتاً معطل)
      // if (db) {
      //   db.close();
      // }

      // نسخ الملف
      fs.copyFileSync(result.filePaths[0], targetPath);

      // إعادة فتح قاعدة البيانات (مؤقتاً معطل)
      // db = new Database(targetPath);

      dialog.showMessageBox({
        type: 'info',
        title: 'نجحت الاستعادة',
        message: 'تم استعادة النسخة الاحتياطية بنجاح'
      });
    }
  } catch (error) {
    dialog.showErrorBox('خطأ', 'فشل في استعادة النسخة الاحتياطية');
  }
}

// معالجات IPC لقاعدة البيانات
ipcMain.handle('db-query', async (event, sql: string, params: any[] = []) => {
  try {
    if (!db) {
      throw new Error('Database not initialized');
    }

    const stmt = db.prepare(sql);
    const result = stmt.all(...params);
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
});

ipcMain.handle('db-run', async (event, sql: string, params: any[] = []) => {
  try {
    if (!db) {
      throw new Error('Database not initialized');
    }

    const stmt = db.prepare(sql);
    const result = stmt.run(...params);
    return { lastID: result.lastInsertRowid, changes: result.changes };
  } catch (error) {
    console.error('Database run error:', error);
    throw error;
  }
});

// أحداث التطبيق
app.whenReady().then(() => {
  initializeDatabase();
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (db) {
      db.close();
    }
    app.quit();
  }
});

app.on('before-quit', () => {
  if (db) {
    db.close();
  }
  console.log('App is closing');
});
