// Database Service Layer for Truck Parts Management System
// طبقة خدمة قاعدة البيانات لنظام إدارة قطع غيار الشاحنات

import { ElectronAPI } from '../preload';

// تعريف أنواع البيانات المحسنة
export interface Part {
  part_id: number;
  part_number: string;
  part_name: string;
  part_name_en: string;
  category_id?: number;
  description?: string;
  purchase_price: number;
  selling_price: number;
  quantity: number;
  min_quantity: number;
  barcode?: string;
  shelf_location?: string;
  reorder_point?: number;
  preferred_supplier_id?: number;
  last_stock_check_date?: string;
  is_active: boolean;
  weight_kg?: number;
  dimensions_cm?: string;
  alternative_part_numbers?: string;
  image_path?: string;
  created_at: string;
  updated_at: string;
}

export interface Customer {
  customer_id: number;
  customer_name: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  customer_type: 'individual' | 'company' | 'workshop' | 'fleet_owner';
  loyalty_points: number;
  last_purchase_date?: string;
  total_spent_amount: number;
  credit_limit: number;
  tax_id_number?: string;
  account_manager_id?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface SalesInvoice {
  invoice_id: number;
  customer_id: number;
  invoice_number: string;
  invoice_date: string;
  total_amount: number;
  discount_amount: number;
  tax_amount: number;
  final_amount: number;
  paid_amount: number;
  payment_status: 'paid' | 'partial' | 'unpaid';
  shipping_address_details?: string;
  shipping_method_id?: number;
  shipping_tracking_number?: string;
  promotion_code_applied?: string;
  sales_channel: 'in_store' | 'online_website' | 'phone_order' | 'social_media';
  original_invoice_id_for_return?: number;
  notes?: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface Debt {
  debt_id: number;
  debt_type: 'receivable' | 'payable';
  customer_id?: number;
  supplier_id?: number;
  related_sales_invoice_id?: number;
  related_purchase_invoice_id?: number;
  debt_reference_number?: string;
  debt_description?: string;
  principal_amount: number;
  currency_code: string;
  interest_rate_annual_percent: number;
  interest_calculation_method: 'simple' | 'compound' | 'none';
  compounding_frequency: 'daily' | 'monthly' | 'quarterly' | 'annually' | 'none';
  interest_accrued: number;
  total_debt_amount_due: number;
  amount_paid: number;
  remaining_balance: number;
  issue_date: string;
  due_date: string;
  payment_terms_details?: string;
  status: 'pending_approval' | 'active' | 'partially_paid' | 'fully_paid' | 'overdue' | 'disputed' | 'written_off' | 'cancelled';
  last_payment_date?: string;
  user_id_created?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// فئة خدمة قاعدة البيانات
export class DatabaseService {
  private static instance: DatabaseService;
  private electronAPI: ElectronAPI;

  private constructor() {
    this.electronAPI = (window as any).electronAPI;
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // ===== Parts Operations =====
  
  async getAllParts(): Promise<Part[]> {
    const query = `
      SELECT * FROM parts 
      WHERE is_active = 1 
      ORDER BY part_name ASC
    `;
    return await this.electronAPI.dbQuery(query);
  }

  async searchParts(searchTerm: string): Promise<Part[]> {
    const query = `
      SELECT * FROM parts 
      WHERE is_active = 1 
      AND (
        part_name LIKE ? OR 
        part_number LIKE ? OR 
        barcode LIKE ? OR
        description LIKE ?
      )
      ORDER BY part_name ASC
    `;
    const term = `%${searchTerm}%`;
    return await this.electronAPI.dbQuery(query, [term, term, term, term]);
  }

  async getPartsByCategory(categoryId: number): Promise<Part[]> {
    const query = `
      SELECT * FROM parts 
      WHERE category_id = ? AND is_active = 1 
      ORDER BY part_name ASC
    `;
    return await this.electronAPI.dbQuery(query, [categoryId]);
  }

  async getLowStockParts(): Promise<Part[]> {
    const query = `
      SELECT * FROM parts 
      WHERE quantity <= min_quantity AND is_active = 1 
      ORDER BY (quantity - min_quantity) ASC
    `;
    return await this.electronAPI.dbQuery(query);
  }

  async getPartByBarcode(barcode: string): Promise<Part | null> {
    const query = `
      SELECT * FROM parts 
      WHERE barcode = ? AND is_active = 1 
      LIMIT 1
    `;
    const results = await this.electronAPI.dbQuery(query, [barcode]);
    return results.length > 0 ? results[0] : null;
  }

  async createPart(part: Omit<Part, 'part_id' | 'created_at' | 'updated_at'>): Promise<number> {
    const query = `
      INSERT INTO parts (
        part_number, part_name, part_name_en, category_id, description,
        purchase_price, selling_price, quantity, min_quantity, barcode,
        shelf_location, reorder_point, preferred_supplier_id, is_active,
        weight_kg, dimensions_cm, alternative_part_numbers, image_path
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      part.part_number, part.part_name, part.part_name_en, part.category_id, part.description,
      part.purchase_price, part.selling_price, part.quantity, part.min_quantity, part.barcode,
      part.shelf_location, part.reorder_point, part.preferred_supplier_id, part.is_active ? 1 : 0,
      part.weight_kg, part.dimensions_cm, part.alternative_part_numbers, part.image_path
    ];
    
    const result = await this.electronAPI.dbRun(query, params);
    return result.lastID;
  }

  async updatePart(partId: number, part: Partial<Part>): Promise<boolean> {
    const fields = [];
    const params = [];
    
    Object.entries(part).forEach(([key, value]) => {
      if (key !== 'part_id' && key !== 'created_at' && value !== undefined) {
        fields.push(`${key} = ?`);
        params.push(key === 'is_active' ? (value ? 1 : 0) : value);
      }
    });
    
    if (fields.length === 0) return false;
    
    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(partId);
    
    const query = `UPDATE parts SET ${fields.join(', ')} WHERE part_id = ?`;
    const result = await this.electronAPI.dbRun(query, params);
    return result.changes > 0;
  }

  async deletePart(partId: number): Promise<boolean> {
    const query = `UPDATE parts SET is_active = 0 WHERE part_id = ?`;
    const result = await this.electronAPI.dbRun(query, [partId]);
    return result.changes > 0;
  }

  // ===== Customer Operations =====
  
  async getAllCustomers(): Promise<Customer[]> {
    const query = `
      SELECT * FROM customers 
      ORDER BY customer_name ASC
    `;
    return await this.electronAPI.dbQuery(query);
  }

  async searchCustomers(searchTerm: string): Promise<Customer[]> {
    const query = `
      SELECT * FROM customers 
      WHERE customer_name LIKE ? OR phone LIKE ? OR email LIKE ?
      ORDER BY customer_name ASC
    `;
    const term = `%${searchTerm}%`;
    return await this.electronAPI.dbQuery(query, [term, term, term]);
  }

  async getCustomersByType(customerType: string): Promise<Customer[]> {
    const query = `
      SELECT * FROM customers 
      WHERE customer_type = ?
      ORDER BY customer_name ASC
    `;
    return await this.electronAPI.dbQuery(query, [customerType]);
  }

  async createCustomer(customer: Omit<Customer, 'customer_id' | 'created_at' | 'updated_at'>): Promise<number> {
    const query = `
      INSERT INTO customers (
        customer_name, contact_person, phone, email, address, city, country,
        customer_type, loyalty_points, last_purchase_date, total_spent_amount,
        credit_limit, tax_id_number, account_manager_id, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      customer.customer_name, customer.contact_person, customer.phone, customer.email,
      customer.address, customer.city, customer.country, customer.customer_type,
      customer.loyalty_points, customer.last_purchase_date, customer.total_spent_amount,
      customer.credit_limit, customer.tax_id_number, customer.account_manager_id, customer.notes
    ];
    
    const result = await this.electronAPI.dbRun(query, params);
    return result.lastID;
  }

  // ===== Sales Operations =====
  
  async getAllSalesInvoices(): Promise<SalesInvoice[]> {
    const query = `
      SELECT * FROM sales_invoices 
      ORDER BY invoice_date DESC
    `;
    return await this.electronAPI.dbQuery(query);
  }

  async getSalesInvoicesByDateRange(startDate: string, endDate: string): Promise<SalesInvoice[]> {
    const query = `
      SELECT * FROM sales_invoices 
      WHERE invoice_date BETWEEN ? AND ?
      ORDER BY invoice_date DESC
    `;
    return await this.electronAPI.dbQuery(query, [startDate, endDate]);
  }

  async getSalesInvoicesByCustomer(customerId: number): Promise<SalesInvoice[]> {
    const query = `
      SELECT * FROM sales_invoices 
      WHERE customer_id = ?
      ORDER BY invoice_date DESC
    `;
    return await this.electronAPI.dbQuery(query, [customerId]);
  }

  // ===== Debt Operations =====
  
  async getAllDebts(): Promise<Debt[]> {
    const query = `
      SELECT * FROM Debts 
      ORDER BY due_date ASC
    `;
    return await this.electronAPI.dbQuery(query);
  }

  async getOverdueDebts(): Promise<Debt[]> {
    const query = `
      SELECT * FROM Debts 
      WHERE status = 'overdue' OR (due_date < date('now') AND status IN ('active', 'partially_paid'))
      ORDER BY due_date ASC
    `;
    return await this.electronAPI.dbQuery(query);
  }

  async getDebtsByCustomer(customerId: number): Promise<Debt[]> {
    const query = `
      SELECT * FROM Debts 
      WHERE customer_id = ?
      ORDER BY due_date ASC
    `;
    return await this.electronAPI.dbQuery(query, [customerId]);
  }

  // ===== Dashboard Statistics =====
  
  async getDashboardStats(): Promise<any> {
    const queries = {
      totalParts: 'SELECT COUNT(*) as count FROM parts WHERE is_active = 1',
      totalCustomers: 'SELECT COUNT(*) as count FROM customers',
      lowStockItems: 'SELECT COUNT(*) as count FROM parts WHERE quantity <= min_quantity AND is_active = 1',
      totalSalesToday: `
        SELECT COALESCE(SUM(final_amount), 0) as total 
        FROM sales_invoices 
        WHERE date(invoice_date) = date('now')
      `,
      totalDebts: `
        SELECT COALESCE(SUM(remaining_balance), 0) as total 
        FROM Debts 
        WHERE debt_type = 'receivable' AND status IN ('active', 'partially_paid', 'overdue')
      `,
      overdueDebts: `
        SELECT COUNT(*) as count 
        FROM Debts 
        WHERE debt_type = 'receivable' AND (status = 'overdue' OR (due_date < date('now') AND status IN ('active', 'partially_paid')))
      `
    };

    const results: any = {};
    
    for (const [key, query] of Object.entries(queries)) {
      const result = await this.electronAPI.dbQuery(query);
      results[key] = result[0]?.count || result[0]?.total || 0;
    }
    
    return results;
  }
}

// تصدير instance واحد
export const dbService = DatabaseService.getInstance();
