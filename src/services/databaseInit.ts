// Database Initialization Service
// خدمة تهيئة قاعدة البيانات

import { ElectronAPI } from '../preload';

export class DatabaseInitService {
  private static instance: DatabaseInitService;
  private electronAPI: ElectronAPI;

  private constructor() {
    this.electronAPI = (window as any).electronAPI;
  }

  public static getInstance(): DatabaseInitService {
    if (!DatabaseInitService.instance) {
      DatabaseInitService.instance = new DatabaseInitService();
    }
    return DatabaseInitService.instance;
  }

  // تهيئة قاعدة البيانات مع البيانات التجريبية
  async initializeDatabase(): Promise<void> {
    try {
      console.log('Initializing database with sample data...');
      
      // إنشاء الجداول الأساسية (إذا لم تكن موجودة)
      await this.createTables();
      
      // إدراج البيانات التجريبية
      await this.insertSampleData();
      
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    // إنشاء جدول الفئات
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS categories (
        category_id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_name TEXT NOT NULL UNIQUE,
        category_name_en TEXT NOT NULL UNIQUE,
        description TEXT,
        parent_category_id INTEGER,
        category_image_path TEXT,
        display_order INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_category_id) REFERENCES categories(category_id) ON DELETE SET NULL
      )
    `);

    // إنشاء جدول الموردين
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS suppliers (
        supplier_id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplier_name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT UNIQUE,
        address TEXT,
        city TEXT,
        country TEXT,
        supplier_rating INTEGER,
        average_lead_time_days INTEGER,
        payment_terms_agreed TEXT,
        bank_account_details TEXT,
        tax_id_number TEXT,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء جدول قطع الغيار
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS parts (
        part_id INTEGER PRIMARY KEY AUTOINCREMENT,
        part_number TEXT NOT NULL UNIQUE,
        part_name TEXT NOT NULL,
        part_name_en TEXT NOT NULL,
        category_id INTEGER,
        description TEXT,
        purchase_price REAL NOT NULL,
        selling_price REAL NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER NOT NULL DEFAULT 5,
        barcode TEXT UNIQUE,
        shelf_location TEXT,
        reorder_point INTEGER,
        preferred_supplier_id INTEGER,
        last_stock_check_date TEXT,
        is_active INTEGER DEFAULT 1 CHECK(is_active IN (0, 1)),
        weight_kg REAL,
        dimensions_cm TEXT,
        alternative_part_numbers TEXT,
        image_path TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE SET NULL,
        FOREIGN KEY (preferred_supplier_id) REFERENCES suppliers(supplier_id) ON DELETE SET NULL
      )
    `);

    // إنشاء جدول العملاء
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS customers (
        customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT UNIQUE,
        address TEXT,
        city TEXT,
        country TEXT,
        customer_type TEXT CHECK(customer_type IN ('individual', 'company', 'workshop', 'fleet_owner')),
        loyalty_points INTEGER DEFAULT 0,
        last_purchase_date TEXT,
        total_spent_amount REAL DEFAULT 0.00,
        credit_limit REAL DEFAULT 0.00,
        tax_id_number TEXT,
        account_manager_id INTEGER,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء جدول فواتير المبيعات
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS sales_invoices (
        invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        invoice_number TEXT NOT NULL UNIQUE,
        invoice_date TEXT NOT NULL,
        total_amount REAL NOT NULL,
        discount_amount REAL DEFAULT 0.00,
        tax_amount REAL DEFAULT 0.00,
        final_amount REAL NOT NULL,
        paid_amount REAL NOT NULL DEFAULT 0.00,
        payment_status TEXT NOT NULL CHECK(payment_status IN ('paid', 'partial', 'unpaid')),
        shipping_address_details TEXT,
        shipping_method_id INTEGER,
        shipping_tracking_number TEXT,
        promotion_code_applied TEXT,
        sales_channel TEXT CHECK(sales_channel IN ('in_store', 'online_website', 'phone_order', 'social_media')),
        original_invoice_id_for_return INTEGER,
        notes TEXT,
        user_id INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE RESTRICT
      )
    `);

    // إنشاء جدول الديون
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS Debts (
        debt_id INTEGER PRIMARY KEY AUTOINCREMENT,
        debt_type TEXT NOT NULL CHECK(debt_type IN ('receivable', 'payable')),
        customer_id INTEGER,
        supplier_id INTEGER,
        related_sales_invoice_id INTEGER,
        related_purchase_invoice_id INTEGER,
        debt_reference_number TEXT UNIQUE,
        debt_description TEXT,
        principal_amount REAL NOT NULL,
        currency_code TEXT DEFAULT 'DZD',
        interest_rate_annual_percent REAL DEFAULT 0.0,
        interest_calculation_method TEXT CHECK(interest_calculation_method IN ('simple', 'compound', 'none')) DEFAULT 'none',
        compounding_frequency TEXT CHECK(compounding_frequency IN ('daily', 'monthly', 'quarterly', 'annually', 'none')) DEFAULT 'none',
        interest_accrued REAL DEFAULT 0.0,
        total_debt_amount_due REAL NOT NULL,
        amount_paid REAL DEFAULT 0.0,
        remaining_balance REAL NOT NULL,
        issue_date TEXT NOT NULL DEFAULT (date('now')),
        due_date TEXT NOT NULL,
        payment_terms_details TEXT,
        status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('pending_approval', 'active', 'partially_paid', 'fully_paid', 'overdue', 'disputed', 'written_off', 'cancelled')),
        last_payment_date TEXT,
        user_id_created INTEGER,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE SET NULL,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id) ON DELETE SET NULL,
        FOREIGN KEY (related_sales_invoice_id) REFERENCES sales_invoices(invoice_id) ON DELETE SET NULL,
        CONSTRAINT chk_debt_party_link CHECK ( (customer_id IS NOT NULL AND supplier_id IS NULL) OR (customer_id IS NULL AND supplier_id IS NOT NULL) )
      )
    `);

    // إنشاء الفهارس المحسنة
    await this.createIndexes();
  }

  private async createIndexes(): Promise<void> {
    const indexes = [
      // Parts indexes
      'CREATE INDEX IF NOT EXISTS idx_parts_part_number ON parts(part_number)',
      'CREATE INDEX IF NOT EXISTS idx_parts_part_name ON parts(part_name)',
      'CREATE INDEX IF NOT EXISTS idx_parts_barcode ON parts(barcode) WHERE barcode IS NOT NULL',
      'CREATE INDEX IF NOT EXISTS idx_parts_quantity_min_quantity ON parts(quantity, min_quantity)',
      'CREATE INDEX IF NOT EXISTS idx_parts_is_active_category ON parts(is_active, category_id)',
      'CREATE INDEX IF NOT EXISTS idx_parts_search_composite ON parts(part_name, part_number, barcode)',
      
      // Customer indexes
      'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name)',
      'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)',
      'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)',
      'CREATE INDEX IF NOT EXISTS idx_customers_type_city ON customers(customer_type, city)',
      
      // Sales indexes
      'CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer_id ON sales_invoices(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_sales_invoices_date_status ON sales_invoices(invoice_date, payment_status)',
      'CREATE INDEX IF NOT EXISTS idx_sales_invoices_invoice_number ON sales_invoices(invoice_number)',
      
      // Debt indexes
      'CREATE INDEX IF NOT EXISTS idx_debts_customer_id ON Debts(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_debts_supplier_id ON Debts(supplier_id)',
      'CREATE INDEX IF NOT EXISTS idx_debts_debt_type_status ON Debts(debt_type, status)',
      'CREATE INDEX IF NOT EXISTS idx_debts_due_date ON Debts(due_date)',
    ];

    for (const indexQuery of indexes) {
      await this.electronAPI.dbRun(indexQuery);
    }
  }

  private async insertSampleData(): Promise<void> {
    // التحقق من وجود البيانات
    const existingParts = await this.electronAPI.dbQuery('SELECT COUNT(*) as count FROM parts');
    if (existingParts[0].count > 0) {
      console.log('Sample data already exists, skipping insertion');
      return;
    }

    // إدراج الفئات
    await this.insertCategories();
    
    // إدراج الموردين
    await this.insertSuppliers();
    
    // إدراج العملاء
    await this.insertCustomers();
    
    // إدراج قطع الغيار
    await this.insertParts();
    
    // إدراج فواتير المبيعات
    await this.insertSalesInvoices();
    
    // إدراج الديون
    await this.insertDebts();
  }

  private async insertCategories(): Promise<void> {
    const categories = [
      ['محرك', 'Engine', 'قطع غيار المحرك'],
      ['فرامل', 'Brakes', 'نظام الفرامل'],
      ['إطارات', 'Tires', 'الإطارات والعجلات'],
      ['كهرباء', 'Electrical', 'النظام الكهربائي'],
      ['تكييف', 'Air Conditioning', 'نظام التكييف'],
      ['ناقل الحركة', 'Transmission', 'ناقل الحركة والقابض'],
      ['التعليق', 'Suspension', 'نظام التعليق'],
      ['الهيكل', 'Body', 'هيكل الشاحنة'],
    ];

    for (const [nameAr, nameEn, description] of categories) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO categories (category_name, category_name_en, description) VALUES (?, ?, ?)',
        [nameAr, nameEn, description]
      );
    }
  }

  private async insertSuppliers(): Promise<void> {
    const suppliers = [
      ['شركة قطع الغيار المتقدمة', 'أحمد محمد', '0123456789', '<EMAIL>', 'شارع الصناعة 123', 'الجزائر', 'الجزائر'],
      ['مؤسسة الشاحنات الحديثة', 'فاطمة علي', '0987654321', '<EMAIL>', 'طريق القاهرة 456', 'الإسكندرية', 'مصر'],
      ['شركة الإمداد السريع', 'محمد حسن', '0555123456', '<EMAIL>', 'شارع الملك فهد 789', 'الرياض', 'السعودية'],
    ];

    for (const [name, contact, phone, email, address, city, country] of suppliers) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO suppliers (supplier_name, contact_person, phone, email, address, city, country, supplier_rating) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [name, contact, phone, email, address, city, country, 4]
      );
    }
  }

  private async insertCustomers(): Promise<void> {
    const customers = [
      ['أحمد محمد', null, '0123456789', '<EMAIL>', 'شارع الاستقلال 123', 'الجزائر', 'الجزائر', 'individual'],
      ['شركة النقل السريع', 'محمد علي', '0987654321', '<EMAIL>', 'المنطقة الصناعية', 'القاهرة', 'مصر', 'company'],
      ['ورشة الإخوة للصيانة', 'سعد أحمد', '0555123456', '<EMAIL>', 'شارع الصناعات', 'الرياض', 'السعودية', 'workshop'],
    ];

    for (const [name, contact, phone, email, address, city, country, type] of customers) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO customers (customer_name, contact_person, phone, email, address, city, country, customer_type, loyalty_points, total_spent_amount, credit_limit) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [name, contact, phone, email, address, city, country, type, 0, 0, 50000]
      );
    }
  }

  private async insertParts(): Promise<void> {
    const parts = [
      ['ENG001', 'فلتر زيت محرك', 'Engine Oil Filter', 1, '1234567890123', 1500, 2000, 25, 10, 'A1-01', 1],
      ['BRK002', 'أقراص فرامل أمامية', 'Front Brake Discs', 2, '2345678901234', 3000, 4000, 5, 8, 'B2-03', 2],
      ['TIR003', 'إطار 315/80R22.5', 'Tire 315/80R22.5', 3, '3456789012345', 25000, 32000, 12, 5, 'C1-01', 3],
      ['ELC004', 'بطارية 12V 100Ah', 'Battery 12V 100Ah', 4, '*************', 15000, 20000, 2, 5, 'D1-02', 1],
      ['AIR005', 'فلتر هواء كابينة', 'Cabin Air Filter', 5, null, 800, 1200, 0, 10, 'E2-01', 2],
    ];

    for (const [partNum, nameAr, nameEn, catId, barcode, purchasePrice, sellPrice, qty, minQty, location, supplierId] of parts) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO parts (part_number, part_name, part_name_en, category_id, barcode, purchase_price, selling_price, quantity, min_quantity, shelf_location, preferred_supplier_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [partNum, nameAr, nameEn, catId, barcode, purchasePrice, sellPrice, qty, minQty, location, supplierId, 1]
      );
    }
  }

  private async insertSalesInvoices(): Promise<void> {
    const invoices = [
      [1, 'INV-2024-001', '2024-05-28', 32000, 0, 0, 32000, 32000, 'paid', 'in_store'],
      [2, 'INV-2024-002', '2024-05-29', 4000, 200, 0, 3800, 0, 'unpaid', 'phone_order'],
      [3, 'INV-2024-003', '2024-05-30', 20000, 0, 0, 20000, 10000, 'partial', 'in_store'],
    ];

    for (const [custId, invNum, date, total, discount, tax, final, paid, status, channel] of invoices) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO sales_invoices (customer_id, invoice_number, invoice_date, total_amount, discount_amount, tax_amount, final_amount, paid_amount, payment_status, sales_channel, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [custId, invNum, date, total, discount, tax, final, paid, status, channel, 1]
      );
    }
  }

  private async insertDebts(): Promise<void> {
    const debts = [
      ['receivable', 2, null, 2, 'DEBT-2024-001', 'دين من فاتورة INV-2024-002', 3800, 3800, 0, 3800, '2024-05-29', '2024-06-15', 'active'],
      ['receivable', 3, null, 3, 'DEBT-2024-002', 'دين من فاتورة INV-2024-003', 10000, 10000, 10000, 0, '2024-05-30', '2024-06-30', 'partially_paid'],
    ];

    for (const [type, custId, suppId, invoiceId, refNum, desc, principal, total, paid, remaining, issueDate, dueDate, status] of debts) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO Debts (debt_type, customer_id, supplier_id, related_sales_invoice_id, debt_reference_number, debt_description, principal_amount, total_debt_amount_due, amount_paid, remaining_balance, issue_date, due_date, status, user_id_created) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [type, custId, suppId, invoiceId, refNum, desc, principal, total, paid, remaining, issueDate, dueDate, status, 1]
      );
    }
  }
}

export const dbInitService = DatabaseInitService.getInstance();
